#!/usr/bin/env python3
"""
Debug script to see what the actual model is predicting vs Poisson.
"""

import sys
import numpy as np
from scipy.stats import poisson

# Add src to path
sys.path.append('src')

from prediction.constants import MODEL_WEIGHT, XG_WEIGHT

def _calculate_poisson_win_probability(team_xg: float, opponent_xg: float, max_goals: int = 10) -> float:
    """Calculate the probability of a team winning based on expected goals using Poisson distribution."""
    try:
        win_prob = 0.0
        for team_goals in range(max_goals + 1):
            for opponent_goals in range(team_goals):  # team_goals > opponent_goals
                team_prob = poisson.pmf(team_goals, team_xg)
                opponent_prob = poisson.pmf(opponent_goals, opponent_xg)
                win_prob += team_prob * opponent_prob
        return max(0.01, min(0.98, win_prob))
    except Exception as e:
        print(f"Error calculating Poisson win probability: {str(e)}")
        return 0.33

def analyze_results():
    """Analyze the actual results from the latest prediction."""
    
    # Results from the latest markdown file
    results = [
        ("Liverpool", "Arsenal", 3.00, 1.75, {"Home": 0.051, "Draw": 0.921, "Away": 0.028}),
        ("Manchester City", "Chelsea", 2.07, 1.07, {"Home": 0.954, "Draw": 0.024, "Away": 0.021}),
        ("Tottenham", "Manchester Utd", 1.59, 0.52, {"Home": 0.744, "Draw": 0.115, "Away": 0.141}),
        ("Newcastle Utd", "Brighton", 1.48, 1.50, {"Home": 0.109, "Draw": 0.142, "Away": 0.749}),
        ("Aston Villa", "Nottm Forest", 1.36, 1.36, {"Home": 0.780, "Draw": 0.195, "Away": 0.025}),
        ("Brentford", "Fulham", 2.02, 0.65, {"Home": 0.197, "Draw": 0.071, "Away": 0.732}),
    ]
    
    print("=" * 100)
    print("🔍 ANALYZING ACTUAL PREDICTION RESULTS")
    print("=" * 100)
    print(f"Current weights: MODEL_WEIGHT={MODEL_WEIGHT}, XG_WEIGHT={XG_WEIGHT}")
    print()
    
    for home_team, away_team, home_xg, away_xg, actual_probs in results:
        print(f"📊 {home_team} vs {away_team}")
        print(f"   Expected Goals: {home_xg:.2f} - {away_xg:.2f}")
        
        # Calculate what Poisson should give us
        home_prob = _calculate_poisson_win_probability(home_xg, away_xg)
        away_prob = _calculate_poisson_win_probability(away_xg, home_xg)
        draw_prob = max(0.05, 1 - home_prob - away_prob)
        
        # Normalize Poisson probabilities
        total_poisson = home_prob + draw_prob + away_prob
        poisson_probs = {
            "Home": home_prob / total_poisson,
            "Draw": draw_prob / total_poisson,
            "Away": away_prob / total_poisson
        }
        
        print(f"   ⚽ Poisson (from xG): Home {poisson_probs['Home']:.1%}, Draw {poisson_probs['Draw']:.1%}, Away {poisson_probs['Away']:.1%}")
        print(f"   🎯 Actual Result:     Home {actual_probs['Home']:.1%}, Draw {actual_probs['Draw']:.1%}, Away {actual_probs['Away']:.1%}")
        
        # Reverse engineer what the model must have predicted
        # actual = MODEL_WEIGHT * model + XG_WEIGHT * poisson
        # model = (actual - XG_WEIGHT * poisson) / MODEL_WEIGHT
        
        model_probs = {}
        for outcome in ["Home", "Draw", "Away"]:
            model_probs[outcome] = (actual_probs[outcome] - XG_WEIGHT * poisson_probs[outcome]) / MODEL_WEIGHT
        
        print(f"   🤖 Inferred Model:    Home {model_probs['Home']:.1%}, Draw {model_probs['Draw']:.1%}, Away {model_probs['Away']:.1%}")
        
        # Check if this makes sense
        if home_xg > away_xg + 0.5:
            expected = "Home"
        elif away_xg > home_xg + 0.5:
            expected = "Away"
        else:
            expected = "Draw"
        
        actual_prediction = max(actual_probs, key=actual_probs.get)
        
        if actual_prediction == expected:
            print(f"   ✅ CONSISTENT: Prediction ({actual_prediction}) aligns with xG expectation ({expected})")
        else:
            print(f"   ❌ INCONSISTENT: Prediction ({actual_prediction}) contradicts xG expectation ({expected})")
            
            # Check if the model is the problem
            model_prediction = max(model_probs, key=model_probs.get)
            if model_prediction != expected:
                print(f"      🔍 ROOT CAUSE: Model predicts {model_prediction}, which is wrong")
                print(f"      💡 SOLUTION: Model needs retraining or different features")
            else:
                print(f"      🔍 ROOT CAUSE: Blending weights issue")
                print(f"      💡 SOLUTION: Increase XG_WEIGHT further")
        
        print()

def test_better_weights():
    """Test what weights would fix the Liverpool vs Arsenal case."""
    
    print("=" * 100)
    print("🔧 TESTING WEIGHT SCENARIOS FOR LIVERPOOL vs ARSENAL")
    print("=" * 100)
    
    home_xg, away_xg = 3.00, 1.75
    
    # Calculate Poisson probabilities
    home_prob = _calculate_poisson_win_probability(home_xg, away_xg)
    away_prob = _calculate_poisson_win_probability(away_xg, home_xg)
    draw_prob = max(0.05, 1 - home_prob - away_prob)
    
    total_poisson = home_prob + draw_prob + away_prob
    poisson_probs = {
        "Home": home_prob / total_poisson,
        "Draw": draw_prob / total_poisson,
        "Away": away_prob / total_poisson
    }
    
    print(f"Poisson probabilities: Home {poisson_probs['Home']:.1%}, Draw {poisson_probs['Draw']:.1%}, Away {poisson_probs['Away']:.1%}")
    
    # Reverse engineer the model prediction from actual result
    actual_probs = {"Home": 0.051, "Draw": 0.921, "Away": 0.028}
    current_model_weight = 0.2
    current_xg_weight = 0.8
    
    model_probs = {}
    for outcome in ["Home", "Draw", "Away"]:
        model_probs[outcome] = (actual_probs[outcome] - current_xg_weight * poisson_probs[outcome]) / current_model_weight
    
    print(f"Inferred model probabilities: Home {model_probs['Home']:.1%}, Draw {model_probs['Draw']:.1%}, Away {model_probs['Away']:.1%}")
    
    # Test different weights
    weight_scenarios = [
        (0.05, 0.95),  # 5% model, 95% xG
        (0.1, 0.9),    # 10% model, 90% xG
        (0.0, 1.0),    # Pure Poisson
    ]
    
    print(f"\nTesting different weights:")
    for model_w, xg_w in weight_scenarios:
        blended_probs = {
            "Home": model_w * model_probs["Home"] + xg_w * poisson_probs["Home"],
            "Draw": model_w * model_probs["Draw"] + xg_w * poisson_probs["Draw"],
            "Away": model_w * model_probs["Away"] + xg_w * poisson_probs["Away"]
        }
        
        # Normalize
        total = sum(blended_probs.values())
        final_probs = {k: v / total for k, v in blended_probs.items()}
        
        max_prob = max(final_probs.values())
        prediction = [k for k, v in final_probs.items() if v == max_prob][0]
        
        print(f"   Weights {model_w:.2f}/{xg_w:.2f}: Home {final_probs['Home']:.1%}, Draw {final_probs['Draw']:.1%}, Away {final_probs['Away']:.1%} → {prediction}")

if __name__ == "__main__":
    analyze_results()
    print()
    test_better_weights()
