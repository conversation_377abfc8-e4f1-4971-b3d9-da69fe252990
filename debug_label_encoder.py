#!/usr/bin/env python3
"""
Quick debug script to check label encoder issue
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from model_training.core import train_model
from feature_engineering.core import prepare_features
from data_loading.core import load_data
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    # Load data
    logger.info("Loading data...")
    data = load_data("ENGLAND_PREMIER_LEAGUE")
    
    # Prepare features
    logger.info("Preparing features...")
    prepared_data = prepare_features(
        data["results"], data["team_stats"], data["league_stats"], 
        data["h2h_stats"], data["league_table"]
    )
    
    # Prepare training data
    columns_to_drop = [
        "result", "three_way", "over_under_1_5", "over_under_2_5", 
        "over_under_3_5", "btts", "home_goals", "away_goals", "total_goals",
        "three_way_encoded", "over_under_1_5_encoded", "over_under_2_5_encoded",
        "over_under_3_5_encoded", "btts_encoded", "form_data_valid_str",
    ]
    
    X = prepared_data.drop([col for col in columns_to_drop if col in prepared_data.columns], axis=1)
    y_dict = {
        "three_way": prepared_data["three_way"],
        "over_under_1_5": prepared_data["over_under_1_5"],
        "over_under_2_5": prepared_data["over_under_2_5"],
        "over_under_3_5": prepared_data["over_under_3_5"],
        "btts": prepared_data["btts"],
    }
    label_encoders = prepared_data.attrs.get("label_encoders", {})
    
    # Train models
    logger.info("Training models...")
    models = train_model(X, y_dict, label_encoders)
    
    # Check what's in the three_way model
    three_way_model = models.get("three_way", {})
    logger.info(f"Three-way model keys: {list(three_way_model.keys())}")
    
    if "encoder" in three_way_model:
        encoder = three_way_model["encoder"]
        logger.info(f"Encoder classes: {encoder.classes_}")
        logger.info(f"Encoder type: {type(encoder)}")
    else:
        logger.error("No encoder found in three_way model!")
        
    # Check label encoders
    logger.info(f"Label encoders keys: {list(label_encoders.keys())}")
    if "three_way" in label_encoders:
        logger.info(f"Label encoder classes: {label_encoders['three_way'].classes_}")

if __name__ == "__main__":
    main()
