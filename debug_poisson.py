#!/usr/bin/env python3
"""
Debug script to test Poisson calculations and blending logic.
"""

import sys
import numpy as np
from scipy.stats import poisson

# Add src to path
sys.path.append('src')

from prediction.constants import MODEL_WEIGHT, XG_WEIGHT

def _calculate_poisson_win_probability(team_xg: float, opponent_xg: float, max_goals: int = 10) -> float:
    """
    Calculate the probability of a team winning based on expected goals using Poisson distribution.
    """
    try:
        win_prob = 0.0

        # Calculate probability for each possible score combination where team wins
        for team_goals in range(max_goals + 1):
            for opponent_goals in range(team_goals):  # team_goals > opponent_goals
                team_prob = poisson.pmf(team_goals, team_xg)
                opponent_prob = poisson.pmf(opponent_goals, opponent_xg)
                win_prob += team_prob * opponent_prob

        return max(0.01, min(0.98, win_prob))  # Ensure reasonable bounds

    except Exception as e:
        print(f"Error calculating Poisson win probability: {str(e)}")
        return 0.33  # Default to equal probability

def test_poisson_calculations():
    """Test Poisson calculations for the problematic matches."""
    
    test_matches = [
        ("Liverpool", "Arsenal", 3.00, 1.75),
        ("Manchester City", "Chelsea", 2.07, 1.07),
        ("Brentford", "Fulham", 2.02, 0.65),
        ("Newcastle Utd", "Brighton", 1.48, 1.50),
        ("Aston Villa", "Nottm Forest", 1.36, 1.36),
    ]
    
    print("=" * 80)
    print("🧮 POISSON PROBABILITY CALCULATIONS")
    print("=" * 80)
    print(f"Current weights: MODEL_WEIGHT={MODEL_WEIGHT}, XG_WEIGHT={XG_WEIGHT}")
    print()
    
    for home_team, away_team, home_xg, away_xg in test_matches:
        print(f"📊 {home_team} vs {away_team}")
        print(f"   Expected Goals: {home_xg:.2f} - {away_xg:.2f}")
        
        # Calculate Poisson probabilities
        home_prob = _calculate_poisson_win_probability(home_xg, away_xg)
        away_prob = _calculate_poisson_win_probability(away_xg, home_xg)
        draw_prob = 1 - home_prob - away_prob
        
        # Ensure draw probability is non-negative
        draw_prob = max(0.05, draw_prob)
        
        # Normalize Poisson probabilities
        total_poisson = home_prob + draw_prob + away_prob
        poisson_probs = {
            "Home": home_prob / total_poisson,
            "Draw": draw_prob / total_poisson,
            "Away": away_prob / total_poisson
        }
        
        print(f"   Poisson Probabilities:")
        print(f"     Home: {poisson_probs['Home']:.1%}")
        print(f"     Draw: {poisson_probs['Draw']:.1%}")
        print(f"     Away: {poisson_probs['Away']:.1%}")
        
        # Test with different model probabilities to see blending effect
        # Simulate problematic model predictions (e.g., heavily favoring draws)
        model_prob_dict = {
            "Home": 0.1,  # Model heavily favors draws
            "Draw": 0.8,
            "Away": 0.1
        }
        
        # Blend model predictions with expected goals
        blended_probs = {
            "Home": MODEL_WEIGHT * model_prob_dict["Home"] + XG_WEIGHT * poisson_probs["Home"],
            "Draw": MODEL_WEIGHT * model_prob_dict["Draw"] + XG_WEIGHT * poisson_probs["Draw"],
            "Away": MODEL_WEIGHT * model_prob_dict["Away"] + XG_WEIGHT * poisson_probs["Away"]
        }
        
        # Normalize blended probabilities
        total = sum(blended_probs.values())
        final_probs = {k: v / total for k, v in blended_probs.items()}
        
        print(f"   Model Probabilities (simulated problematic):")
        print(f"     Home: {model_prob_dict['Home']:.1%}")
        print(f"     Draw: {model_prob_dict['Draw']:.1%}")
        print(f"     Away: {model_prob_dict['Away']:.1%}")
        
        print(f"   Blended Final Probabilities:")
        print(f"     Home: {final_probs['Home']:.1%}")
        print(f"     Draw: {final_probs['Draw']:.1%}")
        print(f"     Away: {final_probs['Away']:.1%}")
        
        # Determine prediction
        max_prob = max(final_probs.values())
        prediction = [k for k, v in final_probs.items() if v == max_prob][0]
        print(f"   🎯 Final Prediction: {prediction}")
        
        # Check if prediction makes sense
        if home_xg > away_xg + 0.5 and prediction != "Home":
            print(f"   ❌ INCONSISTENT: Home team has {home_xg:.2f} xG vs {away_xg:.2f} but prediction is {prediction}")
        elif away_xg > home_xg + 0.5 and prediction != "Away":
            print(f"   ❌ INCONSISTENT: Away team has {away_xg:.2f} xG vs {home_xg:.2f} but prediction is {prediction}")
        elif abs(home_xg - away_xg) <= 0.2 and prediction != "Draw":
            print(f"   ⚠️ QUESTIONABLE: Very close xG ({home_xg:.2f} vs {away_xg:.2f}) but prediction is {prediction}")
        else:
            print(f"   ✅ CONSISTENT: Prediction aligns with expected goals")
        
        print()

def test_weight_scenarios():
    """Test different weight scenarios to find optimal balance."""
    
    print("=" * 80)
    print("🔧 TESTING DIFFERENT WEIGHT SCENARIOS")
    print("=" * 80)
    
    # Test case: Liverpool vs Arsenal (3.00 vs 1.75 xG)
    home_xg, away_xg = 3.00, 1.75
    
    # Calculate Poisson probabilities
    home_prob = _calculate_poisson_win_probability(home_xg, away_xg)
    away_prob = _calculate_poisson_win_probability(away_xg, home_xg)
    draw_prob = max(0.05, 1 - home_prob - away_prob)
    
    total_poisson = home_prob + draw_prob + away_prob
    poisson_probs = {
        "Home": home_prob / total_poisson,
        "Draw": draw_prob / total_poisson,
        "Away": away_prob / total_poisson
    }
    
    print(f"Test case: Liverpool vs Arsenal ({home_xg:.2f} vs {away_xg:.2f} xG)")
    print(f"Poisson probabilities: Home {poisson_probs['Home']:.1%}, Draw {poisson_probs['Draw']:.1%}, Away {poisson_probs['Away']:.1%}")
    print()
    
    # Problematic model prediction (heavily favoring draws)
    model_prob_dict = {"Home": 0.1, "Draw": 0.8, "Away": 0.1}
    
    weight_scenarios = [
        (0.0, 1.0),  # Pure Poisson
        (0.2, 0.8),  # 20% model, 80% Poisson
        (0.4, 0.6),  # Current weights
        (0.6, 0.4),  # 60% model, 40% Poisson
        (0.8, 0.2),  # 80% model, 20% Poisson
        (1.0, 0.0),  # Pure model
    ]
    
    for model_w, xg_w in weight_scenarios:
        blended_probs = {
            "Home": model_w * model_prob_dict["Home"] + xg_w * poisson_probs["Home"],
            "Draw": model_w * model_prob_dict["Draw"] + xg_w * poisson_probs["Draw"],
            "Away": model_w * model_prob_dict["Away"] + xg_w * poisson_probs["Away"]
        }
        
        total = sum(blended_probs.values())
        final_probs = {k: v / total for k, v in blended_probs.items()}
        
        max_prob = max(final_probs.values())
        prediction = [k for k, v in final_probs.items() if v == max_prob][0]
        
        print(f"Weights {model_w:.1f}/{xg_w:.1f}: Home {final_probs['Home']:.1%}, Draw {final_probs['Draw']:.1%}, Away {final_probs['Away']:.1%} → {prediction}")

if __name__ == "__main__":
    test_poisson_calculations()
    print()
    test_weight_scenarios()
