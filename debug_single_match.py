#!/usr/bin/env python3
"""
Debug script to test a single match prediction and see model outputs.
"""

import sys
import os
import logging
from datetime import datetime
import pandas as pd
import numpy as np

# Add src to path
sys.path.append('src')

from data_loading import load_data, get_available_leagues
from scrapers.config import LEAGUE_CONFIGS
from feature_engineering import prepare_features
from model_training import train_model
from prediction import predict_match

# Setup logging to see debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

logger = logging.getLogger(__name__)

def test_single_match():
    """Test a single problematic match to see model outputs."""
    
    logger.info("=" * 80)
    logger.info("🔍 SINGLE MATCH DEBUG: Liverpool vs Arsenal")
    logger.info("=" * 80)
    
    try:
        # Load data
        league_name = "ENGLAND_PREMIER_LEAGUE"
        available_leagues = get_available_leagues()
        if league_name not in available_leagues:
            logger.error(f"❌ {league_name} not found")
            return

        league_config = LEAGUE_CONFIGS.get(league_name)
        if not league_config:
            logger.error(f"❌ No configuration found for {league_name}")
            return

        data_tuple, data_info = load_data(league_name, league_config)
        if data_tuple is None:
            logger.error("Failed to load data")
            return

        results, team_stats, league_stats, h2h_stats, league_table = data_tuple
        data = {
            'results': results,
            'team_stats': team_stats,
            'league_stats': league_stats,
            'h2h_stats': h2h_stats,
            'league_table': league_table
        }
        
        logger.info("✅ Data loaded successfully")
        
        # Prepare features
        prepared_data = prepare_features(
            data['results'], data['team_stats'], data['league_stats'],
            data['h2h_stats'], data['league_table'],
            league_config.get('COLUMN_MAPPING', {}),
            league_config.get('TEAM_NAME_MAPPING', {})
        )
        
        if prepared_data is None or prepared_data.empty:
            logger.error("Failed to prepare features")
            return
        
        logger.info(f"✅ Features prepared: {prepared_data.shape}")
        
        # Prepare training data
        nan_columns = prepared_data.columns[prepared_data.isna().any()].tolist()
        if nan_columns:
            logger.warning(f"⚠️ NaN values found, filling with 0")
            prepared_data = prepared_data.fillna(0)

        # Create target variables
        from sklearn.preprocessing import LabelEncoder

        y_dict = {}
        label_encoders = {}

        # Three-way prediction
        if 'result' in prepared_data.columns:
            le_three_way = LabelEncoder()
            y_dict['three_way'] = pd.Series(le_three_way.fit_transform(prepared_data['result']))
            label_encoders['three_way'] = le_three_way

        # Over/Under 2.5 prediction
        if 'total_goals' in prepared_data.columns:
            le_ou = LabelEncoder()
            ou_labels = (prepared_data['total_goals'] > 2.5).astype(str)
            y_dict['over_under_2_5'] = pd.Series(le_ou.fit_transform(ou_labels))
            label_encoders['over_under_2_5'] = le_ou

        # BTTS prediction
        if 'home_goals' in prepared_data.columns and 'away_goals' in prepared_data.columns:
            le_btts = LabelEncoder()
            btts_labels = ((prepared_data['home_goals'] > 0) & (prepared_data['away_goals'] > 0)).astype(str)
            y_dict['btts'] = pd.Series(le_btts.fit_transform(btts_labels))
            label_encoders['btts'] = le_btts

        # Remove target columns
        exclude_cols = ['result', 'total_goals', 'home_goals', 'away_goals',
                       'home_team', 'away_team', 'date', 'season',
                       'three_way', 'three_way_encoded',
                       'over_under_1_5', 'over_under_1_5_encoded',
                       'over_under_2_5', 'over_under_2_5_encoded',
                       'over_under_3_5', 'over_under_3_5_encoded',
                       'btts', 'btts_encoded']
        feature_cols = [col for col in prepared_data.columns if col not in exclude_cols]
        X = prepared_data[feature_cols]

        # Convert to numeric
        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)

        logger.info(f"Training data shape: {X.shape}")
        
        # Train models
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X, columns=feature_cols)

        models = train_model(X, y_dict, label_encoders)
        if not models:
            logger.error("❌ No models trained")
            return

        logger.info("✅ Models trained successfully")
        
        # Make prediction for Liverpool vs Arsenal
        home_team, away_team = "Liverpool", "Arsenal"
        logger.info(f"\n🎯 Predicting: {home_team} vs {away_team}")
        
        predictions, error_msg, correct_scores = predict_match(
            models=models,
            home_team=home_team,
            away_team=away_team,
            team_stats=data['team_stats'],
            league_stats=data['league_stats'],
            h2h_stats=data['h2h_stats'],
            league_table=data['league_table'],
            column_mapping={},
            feature_names=models.get('three_way', {}).get('feature_names', []),
            avg_goals_per_match=2.5,
            label_encoders={pred_type: models[pred_type].get('encoder') for pred_type in models.keys()},
            log_features=False
        )
        
        if error_msg:
            logger.error(f"❌ Prediction failed: {error_msg}")
            return
        
        # Log results
        three_way = predictions.get('three_way', {})
        if three_way:
            prediction = three_way.get('prediction', 'N/A')
            probs = three_way.get('probabilities', {})
            
            logger.info(f"\n📊 FINAL RESULTS:")
            logger.info(f"🎯 Prediction: {prediction}")
            if probs:
                for outcome, prob in probs.items():
                    logger.info(f"   {outcome}: {prob:.1%}")
        
        # Expected goals
        expected_goals = predictions.get('expected_goals', {})
        if expected_goals:
            logger.info(f"⚽ Expected Goals: {expected_goals.get('home', 0):.2f} - {expected_goals.get('away', 0):.2f}")
        
    except Exception as e:
        logger.error(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_match()
