# Decision Log: Automated Sports Betting Analysis System

## 2025-05-14 13:32:00
### Decision: Add `decisionLog.md` to Memory Bank and update Custom Instructions
-   **Rationale**: To provide a dedicated and chronological, timestamped record of key project decisions, their reasoning, and impact. This enhances project transparency and recall. The custom instructions for Cline were updated to reflect this new file and its role.
-   **Alternatives Considered**: Incorporating decisions into `activeContext.md` or `progress.md`. A separate log offers better focus. Storing custom instructions only in the chat.
-   **Impact/Scope**: Affects Memory Bank structure and documentation workflow for Cline. A `custom-instructions.md` file was created in the project root.
-   **Originator/Approval**: User Request, Cline Implementation.

---

## 2025-05-14 13:33:00
### Decision: Use SQLite for Database Migration
-   **Rationale**: SQLite is serverless, file-based, integrates well with Python, and is suitable for the project's current scale and local development context.
-   **Alternatives Considered**: Other database systems (e.g., PostgreSQL, MySQL), but SQLite offers simplicity for this task.
-   **Impact/Scope**: The data ingestion script will use the `sqlite3` Python module. A `.db` file will be created in the project.
-   **Originator/Approval**: Cline Proposal, User Approved.

---

## 2025-05-14 13:41:00
### Decision: Refine Season Handling for Database Tables
-   **Rationale**: User clarified that some data (league table, league stats, team stats) is for the current season (2024-2025), while other data (H2H, match results) spans multiple seasons for historical context.
-   **Alternatives Considered**: Treating all data as current season, or trying to infer season for all data types.
-   **Impact/Scope**:
    -   `league_tables`, `league_summary_stats`, `team_detailed_stats`: `season` column will be "2024-2025".
    -   `match_results`: `season` column will be inferred from `match_date`. Dates without years (e.g., from `_results.csv`) will assume current season context (2024 or 2025 based on month) for year inference.
    -   `head_to_head_stats`: Aggregate stats' `season` column will be "as_of_2024-2025". Embedded recent match strings retain their original dates.
-   **Originator/Approval**: User Clarification, Cline Implementation.

---

## 2025-05-18 21:50:00
### Decision: Investigate NETHERLANDS_EREDIVISIE H2H Scraping Failure
-   **Rationale**: User reported `test_eredivisie_scrape.py` failing with "Incomplete team data assignment" for "Afc Ajax". This blocks H2H data collection for this league.
-   **Alternatives Considered**: Ignoring the error (not viable).
-   **Impact/Scope**: Affects H2H data integrity for NETHERLANDS_EREDIVISIE. Requires debugging `scrape_footystats_h2h.py` and `src/scrapers/team_mappings.py`.
-   **Originator/Approval**: User Report, AI Investigation.

---

## 2025-05-18 21:52:00
### Decision: Target `MANUAL_OVERRIDES` for "Afc Ajax" Mapping
-   **Rationale**: Diagnosis indicated a mismatch between "Afc Ajax" (config) and "Ajax" (scraped name), not properly reconciled by `TEAM_NAME_MAPPING`. Adding `"afc ajax": "ajax",` to `MANUAL_OVERRIDES` in `src/scrapers/team_mappings.py` was identified as the most direct fix.
-   **Alternatives Considered**: Modifying normalization logic in `scrape_footystats_h2h.py` (more complex, potential side effects). Relying purely on dynamic mapping (failed in this case).
-   **Impact/Scope**: Requires modification of `src/scrapers/team_mappings.py`. Aims to ensure "Afc Ajax" correctly maps to the canonical "ajax".
-   **Originator/Approval**: AI Proposal, Implicit User Concurrence.

---

## 2025-05-18 21:54:00
### Decision: Request Manual File Edit by User After `replace_in_file` Failures
-   **Rationale**: Multiple AI attempts to add `"afc ajax": "ajax",` to `MANUAL_OVERRIDES` in `src/scrapers/team_mappings.py` using the `replace_in_file` tool failed to modify the file. To proceed with the fix, the user was requested to make the change manually.
-   **Alternatives Considered**: Further attempts with `replace_in_file` with different parameters (deemed inefficient after several failures), attempting `write_to_file` (higher risk for a small change).
-   **Impact/Scope**: Shifts the file modification task to the user. Delays automated resolution. Highlights potential limitations or specific issues with `replace_in_file` for this context.
-   **Originator/Approval**: AI Decision (due to tool failure).

---

## 2025-05-18 22:10:00
### Decision: Refactor Team Mapping System to Per-League Files
-   **Rationale**: User requested a change from a single `MANUAL_OVERRIDES` dictionary to a system where each league has its own mapping file in a dedicated subdirectory. This aims to improve organization and scalability of team name mappings. This supersedes the immediate fix for "Afc Ajax" as that mapping will be part of the new structure.
-   **Alternatives Considered**: Proceeding with the single file fix (denied by user).
-   **Impact/Scope**:
    -   Creation of a new subdirectory (e.g., `src/scrapers/team_mapping_configs/`).
    -   Migration of existing `MANUAL_OVERRIDES` into new per-league files.
    -   Modification of `src/scrapers/team_mappings.py` to load and consolidate mappings from these new files.
    -   The "Afc Ajax" to "ajax" mapping will be incorporated into the new file for NETHERLANDS_EREDIVISIE.
-   **Originator/Approval**: User Request.

---

## 2025-05-18 22:20:00
### Decision: Confirm Team Mapping Refactor Success
-   **Rationale**: User confirmed that the refactoring of the team mapping system to per-league files (in `src/scrapers/team_mapping_configs/`) and the update to `src/scrapers/team_mappings.py` to load them was successful. The `test_eredivisie_scrape.py` script now works, resolving the "Afc Ajax" mapping issue.
-   **Alternatives Considered**: N/A - this was a confirmation of a completed step.
-   **Impact/Scope**: The team mapping system is now more modular. The immediate bug with Eredivisie H2H scraping related to "Afc Ajax" is resolved at the `team_mappings.py` level.
-   **Originator/Approval**: User Confirmation.

---

## 2025-05-18 22:45:00
### Decision: Enhance Failed Matchup Logging in `scrape_footystats_h2h.py`
-   **Rationale**: User requested improved logging for failed H2H scrapes to make it easier to identify problematic matchups and their failure reasons.
-   **Alternatives Considered**: Relying solely on console output or existing HTML saving for failures (less structured for analysis).
-   **Impact/Scope**:
    -   Modify `scrape_footystats_h2h.py`.
    -   Introduce Python's `logging` module for this purpose.
    -   Create a new log file (e.g., `h2h_results/h2h_failed_matchups.log`).
    -   Log details: league, matchup string, URL, and failure reason.
-   **Originator/Approval**: User Request.

---

## 2025-05-20 00:15:00
### Decision: Add "RCD Mallorca" Mapping for SPAIN_LA_LIGA
-   **Rationale**: To fix team name mapping issues identified in `h2h_results/h2h_failed_matchups.log` where "RCD Mallorca" was not being correctly mapped to "Real Club Deportivo Mallorca".
-   **Alternatives Considered**: Modifying the `team_name_mapper.py` logic (more complex for a specific case).
-   **Impact/Scope**: Updated [`team_mappings/SPAIN_LA_LIGA_team_mapping.json`](team_mappings/SPAIN_LA_LIGA_team_mapping.json:13) to include `"RCD Mallorca": "Real Club Deportivo Mallorca"`. This should resolve the specific H2H scraping failures for this team in La Liga.
-   **Originator/Approval**: User Request, AI Implementation.

---

## 2025-05-22 12:41:00
### Decision: Create H2H Test Script and Refine H2H Scraper (`get_head_to_head_stats`)
-   **Rationale**: To ensure the `get_head_to_head_stats` function in [`src/scrapers/head_to_head.py`](src/scrapers/head_to_head.py:1) is working correctly, to identify and fix bugs related to imports, parameter handling, and data assignment, and to provide a repeatable way to test its functionality.
-   **Alternatives Considered**:
-   Manual testing of the scraper (less efficient, not easily repeatable).
-   Not creating a dedicated test script (higher risk of regressions, harder to isolate issues).
-   Making direct, untested changes to the scraper.
-   **Impact/Scope**:
-   Created a new test script: [`src/tests/test_h2h_scraper.py`](src/tests/test_h2h_scraper.py:1).
-   Modified [`src/scrapers/head_to_head.py`](src/scrapers/head_to_head.py:1) to use relative imports for `utils` and `config` to resolve `ModuleNotFoundError`.
-   Modified [`src/scrapers/utils.py`](src/scrapers/utils.py:1) by updating the `safe_extract` function to accept `use_direct_element` and `selector=None` as parameters, and added `Union` and `Tag` to type hints to resolve a `TypeError`.
-   Refactored [`src/scrapers/head_to_head.py`](src/scrapers/head_to_head.py:1) to implement dynamic on-page team name mapping. This involved extracting team names displayed on the page and comparing them to URL-derived names to ensure stats (win %, goals, clean sheets) are assigned to the correct team dictionary keys, resolving the inverted stats issue.
-   The test script was updated to test multiple H2H URLs.
-   Confirmed that `recent_results` parsing is correct based on the data available in the targeted HTML section of the live page; 2025 past results were not present in that specific section for the tested URLs.
-   **Originator/Approval**: User Request, AI Implementation & Debugging.

---

## 2025-05-23 10:39:00
### Decision: Implement Comprehensive H2H Scraping Fixes for Serbia Prva Liga
-   **Rationale**: User reported H2H scraping failures for Serbia Prva Liga with "No title tag found" errors and team name mapping issues. The website was blocking requests after ~4 successful scrapes, requiring a systematic solution.
-   **Alternatives Considered**: 
    - Ignoring the issue (not viable for production)
    - Simple retry logic without understanding root cause
    - Manual intervention for each failure
-   **Impact/Scope**:
    - Enhanced [`src/scrapers/head_to_head.py`](src/scrapers/head_to_head.py:1) with 8 fallback selectors and comprehensive error handling
    - Created [`src/scrapers/team_mappings.py`](src/scrapers/team_mappings.py:1) with 87 Serbian team variations
    - Added Serbian diacritic normalization and city/regional identifier handling
    - Implemented robust validation and detailed logging for debugging
-   **Originator/Approval**: User Report, AI Investigation & Implementation.

---

## 2025-05-23 11:18:00
### Decision: Implement Batch Processing System (4 URLs per run)
-   **Rationale**: Testing revealed that websites block scraping after approximately 4 successful requests. A batch processing system was needed to handle this rate limiting systematically while ensuring all URLs eventually get processed.
-   **Alternatives Considered**:
    - Longer delays between requests (insufficient, still gets blocked)
    - Different user agents or headers (temporary solution at best)
    - Ignoring failed URLs (unacceptable data loss)
-   **Impact/Scope**:
    - Created [`src/scrapers/h2h_batch_scraper.py`](src/scrapers/h2h_batch_scraper.py:1) for batch processing
    - Implemented checkpoint system in `checkpoints/` directory for progress tracking
    - Modified [`src/scrapers/main.py`](src/scrapers/main.py:1) to automatically use batch processing for large URL sets
    - Created [`run_h2h_batch.py`](run_h2h_batch.py:1) as user-friendly wrapper script
-   **Originator/Approval**: User Request, AI Implementation.

---

## 2025-05-23 12:24:00
### Decision: Implement Process Restart Strategy for Rate Limiting Avoidance
-   **Rationale**: Testing showed that running multiple batches in the same process still triggered rate limiting. The solution required completely restarting the Python process between batches to avoid detection.
-   **Alternatives Considered**:
    - Longer delays between batches (insufficient)
    - Session clearing within same process (ineffective)
    - Different scraping libraries (would require major refactoring)
-   **Impact/Scope**:
    - Modified [`src/scrapers/h2h_automation.py`](src/scrapers/h2h_automation.py:1) to run single iterations only
    - Created [`run_h2h_automation.sh`](run_h2h_automation.sh:1) shell script for process management
    - Implemented random delays (5-10 seconds) between process restarts
    - Added comprehensive logging and progress tracking across restarts
-   **Originator/Approval**: User Request, AI Implementation.

---

## 2025-05-23 13:45:00
### Decision: Implement Retry Mechanism for Failed URLs
-   **Rationale**: The batch system initially marked URLs as permanently failed after first failure. A retry mechanism was needed to give failed URLs additional attempts, as failures could be due to temporary network issues or rate limiting.
-   **Alternatives Considered**:
    - Manual retry of failed URLs (not scalable)
    - Immediate retry (would trigger rate limiting)
    - Ignoring failed URLs (unacceptable data loss)
-   **Impact/Scope**:
    - Enhanced [`src/scrapers/h2h_batch_scraper.py`](src/scrapers/h2h_batch_scraper.py:1) with retry counter system
    - Implemented cooldown logic (retry after processing 20 other URLs)
    - Added maximum retry limit (3 attempts) before permanent failure
    - Updated checkpoint structure to track retry attempts and timing
    - Migrated existing failed URLs to new retry system
-   **Originator/Approval**: User Request, AI Implementation.

---

## 2025-05-23 19:33:00
### Decision: Fix H2H Automation Single Iteration Logic
-   **Rationale**: The `run_single_iteration()` function in `h2h_automation.py` was checking other leagues for remaining work and returning `True` if any other league had work, causing the shell script to continue running instead of exiting after processing one batch of 4 URLs. This defeated the purpose of "single iteration" processing.
-   **Alternatives Considered**:
    - Modifying the shell script logic (would be more complex and less clear)
    - Adding flags to control cross-league checking (unnecessary complexity)
    - Ignoring the issue (unacceptable for production automation)
-   **Impact/Scope**:
    - Removed lines 122-143 from `run_single_iteration()` that checked other leagues
    - Added missing `load_checkpoint()` function to `h2h_batch_scraper.py` to resolve import error
    - Simplified return logic to only consider current league's work status
    - Function now returns `True` if current league has more work, `False` if complete
    - Enables proper process restart strategy for rate limiting avoidance
-   **Originator/Approval**: User Report, AI Investigation & Implementation.

---

---

## 2025-05-23 20:15:00
### Decision: H2H Automation System Debugging Initiative
-   **Rationale**: System was experiencing critical issues including retry deadlocks where failed URLs couldn't be retried, checkpoint corruption causing incorrect progress tracking, and auto-selection logic incorrectly identifying completed leagues as having remaining work.
-   **Alternatives Considered**: Rebuilding system from scratch, manual intervention for each issue, ignoring the problems.
-   **Impact/Scope**: Restored full automation functionality with proper league progression, data integrity, and robust error handling. Enables continuous H2H data collection across all leagues.
-   **Originator/Approval**: Debug Analysis, User Request.

---

## 2025-05-23 20:30:00
### Decision: Fix Retry Eligibility Logic Deadlock
-   **Rationale**: Original logic prevented retries when all URLs were processed but some had failed (position >= total_urls), causing deadlock where 4 failed URLs couldn't be retried due to insufficient cooldown.
-   **Alternatives Considered**: Resetting position counter, changing batch selection logic, increasing cooldown threshold.
-   **Impact/Scope**: Modified `_check_retry_eligibility()` in `h2h_batch_scraper.py` to allow retries when reaching end of URL list. Eliminated retry deadlocks and enabled proper processing of failed URLs.
-   **Originator/Approval**: Retry System Analysis, Implementation.

---

## 2025-05-23 20:35:00
### Decision: Enhance Batch Selection for Retry URLs
-   **Rationale**: Retry URLs were only selected if they appeared in the next sequential batch, limiting retry opportunities and causing inefficient processing.
-   **Alternatives Considered**: Maintaining sequential processing, separate retry batches.
-   **Impact/Scope**: Updated `_get_next_batch()` to prioritize retry URLs regardless of their position in the URL list, ensuring failed URLs are processed before new ones.
-   **Originator/Approval**: Batch Processing Optimization.

---

## 2025-05-23 20:45:00
### Decision: Implement Duplicate Prevention in CSV Saving
-   **Rationale**: System was appending duplicate entries when checkpoint data was inconsistent with CSV data, causing data corruption and inflated progress counts.
-   **Alternatives Considered**: Rebuilding CSV files, ignoring duplicates in analysis, post-processing cleanup.
-   **Impact/Scope**: Enhanced `save_head_to_head_stats_to_csv()` in `utils.py` to check for existing matchups and filter out duplicates before saving. Ensures data integrity and prevents CSV file corruption.
-   **Originator/Approval**: Data Quality Assurance.

---

## 2025-05-23 20:50:00
### Decision: Implement CSV Fallback Loading Strategy
-   **Rationale**: Checkpoint files can be deleted or corrupted, but CSV files contain the actual completed work. System needed robust progress tracking that survives checkpoint file issues.
-   **Alternatives Considered**: Always relying on checkpoints, manual progress tracking, rebuilding checkpoints from scratch.
-   **Impact/Scope**: Added `_load_existing_csv_data()` method to load completed URLs from CSV files when checkpoints are missing. Provides resilient progress tracking.
-   **Originator/Approval**: Resilience Engineering.

---

## 2025-05-23 21:00:00
### Decision: Fix Auto-Selection Logic for Completed Leagues
-   **Rationale**: Auto-selection was incorrectly identifying completed leagues as having remaining work because `load_checkpoint()` returned empty data when no checkpoint existed, ignoring CSV completion data.
-   **Alternatives Considered**: Manual league specification, ignoring completed leagues, always creating checkpoint files.
-   **Impact/Scope**: Updated `load_checkpoint()` function to check CSV files when no checkpoint exists, enabling proper league progression and automatic detection of completed work (e.g., SERBIA_PRVA_LIGA with 120/120 URLs).
-   **Originator/Approval**: Automation Logic Enhancement.

---

## 2025-05-23 21:05:00
### Decision: H2H Automation System Debugging Completion
-   **Rationale**: All identified issues have been systematically resolved and tested. System now properly handles retry deadlocks, prevents data corruption, maintains accurate progress tracking, and correctly progresses between leagues.
-   **Alternatives Considered**: Partial fixes, ongoing manual intervention.
-   **Impact/Scope**: 
    -   ✅ SERBIA_PRVA_LIGA: Correctly detected as complete (120/120 URLs)
    -   ✅ ENGLAND_LEAGUE_1: Successfully processing (12/276 URLs completed)
    -   ✅ Batch Processing: Exactly 4 URLs per iteration
    -   ✅ Progress Tracking: Accurate progress reporting
    -   ✅ Duplicate Prevention: Working correctly
    -   ✅ Shell Script Integration: Proper iteration management
-   **Originator/Approval**: System Validation, User Confirmation.

---

## 2025-05-29 09:55:00
### Decision: Correct H2H Automation Loop for Retryable URLs (Attempt 1)
-   **Rationale**: The `@run_h2h_automation.sh` script was hanging on leagues (e.g., AUSTRALIA_SOUTH_AUSTRALIA_NPL) that had URLs pending retry. The `run_batch` method in `src/scrapers/h2h_batch_scraper.py` was incorrectly reporting "no more work" if the only pending URLs were those awaiting retry cooldown, causing a loop. This was the first attempt to fix this.
-   **Alternatives Considered**: Modifying logic in `h2h_automation.py` (less direct).
-   **Impact/Scope**:
    -   Modified the return logic of the `run_batch` method in `src/scrapers/h2h_batch_scraper.py`.
    -   It was intended to correctly return `True` (more work remains) if `len(completed_urls) + len(permanently_failed_urls) < total_urls`.
    -   This change was insufficient as the method would return `False` early if `_get_next_batch()` was empty, bypassing the new logic.
-   **Originator/Approval**: User Report, AI Investigation & Implementation.

---

## 2025-05-29 10:15:00
### Decision: Correct H2H Automation Loop for Retryable URLs (Attempt 2 - Robust Fix to `run_batch`)
-   **Rationale**: The first fix for the H2H automation loop was insufficient. The `run_batch` method in `src/scrapers/h2h_batch_scraper.py` still returned `False` prematurely if `_get_next_batch()` yielded no URLs (e.g., due to retry cooldowns), without checking the overall league completion status.
-   **Alternatives Considered**: Further minor tweaks to the previous logic.
-   **Impact/Scope**:
    -   Refactored the `run_batch` method in `src/scrapers/h2h_batch_scraper.py`.
    -   The method now *always* determines its return value based on the overall league completion status (`len(completed_urls) + len(permanently_failed_urls) < total_urls`), regardless of whether the current call to `_get_next_batch()` returned an empty list.
    -   `_save_progress()` is also called even if the batch is empty to persist state changes from `_check_retry_eligibility()`.
    -   This ensures the automation script correctly identifies ongoing work (including pending retries) and continues iterating until true completion or max retries for URLs.
-   **Originator/Approval**: AI Diagnosis & Implementation following failed test.

---

## 2025-05-29 12:45:00
### Decision: Correct H2H Automation Loop for Retryable URLs (Attempt 3 - Refine `_check_retry_eligibility`)
-   **Rationale**: Even with fixes to `run_batch`, the automation script was still looping indefinitely on leagues with only retryable URLs remaining. This was because `_check_retry_eligibility` in `src/scrapers/h2h_batch_scraper.py` would not make URLs eligible for retry if the `processed_since_last_retry` counter was 0 (due to all attempts failing) and the `current_position` hadn't reached `total_urls`.
-   **Alternatives Considered**: Introducing an "empty batch iteration" counter to force retries (more complex).
-   **Impact/Scope**:
    -   Modified `_check_retry_eligibility` in `src/scrapers/h2h_batch_scraper.py`.
    -   A new condition `all_initial_attempts_done_and_failures_exist` was added. This condition is true if:
        1.  All URLs in the league have been categorized (i.e., `len(completed_urls) + len(failed_urls) + len(permanently_failed_urls) == total_urls`).
        2.  AND there are URLs currently in `failed_urls` (i.e., awaiting retry).
    -   Retries are now triggered if `cooldown_met` (based on `processed_since_last_retry`) OR `all_initial_attempts_done_and_failures_exist` is true.
    -   This ensures that if all "new" work is done and only failed URLs remain, they will be made eligible for retry, breaking the loop and allowing them to proceed through their retry attempts or become permanently failed.
-   **Originator/Approval**: AI Diagnosis & Implementation following further failed test.

---

## 2025-05-29 13:15:00
### Decision: Refine Logging in `_check_retry_eligibility` and Confirm Loop Fix
-   **Rationale**: The H2H automation loop issue was confirmed resolved after the third fix attempt. A minor logging improvement was needed in `_check_retry_eligibility` to accurately state the reason for triggering retries.
-   **Alternatives Considered**: Leaving the logging as is (less clear for future debugging).
-   **Impact/Scope**:
    -   Modified the logging message in `_check_retry_eligibility` in `src/scrapers/h2h_batch_scraper.py` to use the `reason_for_retry_check` variable, which accurately reflects whether retries were triggered by "cooldown met" or "all initial attempts done and failures exist".
    -   Confirmed that the `AUSTRALIA_SOUTH_AUSTRALIA_NPL` league now processes correctly, with all retryable URLs being attempted up to the maximum limit and then marked as permanently failed, leading to league completion.
    -   The H2H automation system is now considered robust in handling these retry scenarios.
-   **Originator/Approval**: AI Implementation & User Confirmation.
