# Project Brief: Automated Sports Betting Analysis and Prediction System

## 1. Project Vision
To develop an automated system that collects, processes, and analyzes sports data (primarily football/soccer) to generate betting predictions. The system aims to streamline the data pipeline from initial league configuration to final prediction output.

## 2. Core Functionalities
The project encompasses several key stages:

### 2.1. Configuration Management
-   **Initial Setup**: Begins with a `leagues.json` file defining leagues and their core URLs (stats, table, fixtures).
-   **URL Extraction**: Scripts (`url_extractor.py`) process `leagues.json` to extract detailed fixture-specific URLs (team URLs, head-to-head URLs) and store them in individual league JSON configuration files (e.g., in `src/config/json_database/`).
-   **Configuration Transformation**: Scripts manage the conversion and organization of these JSON configurations into Python dictionary formats (`overwrite_config.py`) and split them into per-league Python configuration files (`split_config.py`, e.g., in `src/scrapers/league_configs/`).
-   **URL Management**: Utilities to modify configuration files, such as commenting out specific URLs for prediction runs (`comment_urls.py`).

### 2.2. Data Acquisition (Scraping)
-   **Comprehensive Scraping**: The `main.py` script orchestrates the scraping of various data points for configured leagues, including:
    -   League tables
    -   Match results (historical and recent)
    -   Head-to-head statistics between teams
    -   Overall league statistics
    -   Individual team statistics
-   **Data Storage (Initial)**: Scraped data is currently saved as CSV files, organized into league-specific directories under `data/raw/`.

### 2.3. Data Processing and Reconciliation
-   **Team Name Mapping**: A crucial step (`team_name_mapper.py`) involves reconciling team names that may differ across various data sources (e.g., stats sites vs. fixture sites) using fuzzy matching.

### 2.4. Prediction Generation
-   **Model Execution**: The `testing_prediction.py` script utilizes the processed (currently CSV) data to run predictive models.
-   **Output**: Predictions are generated and saved as Excel files, organized by league, in the `data/processed/` directory.

### 2.5. Data Persistence (Enhancement - Current Task)
-   **Database Integration**: The current initiative involves migrating data from CSV files (and potentially future scraped data directly) into a structured database (e.g., SQLite). This aims to:
    -   Improve data retrieval efficiency.
    -   Provide a more robust and queryable data store.
    -   Facilitate more complex data analysis and feature engineering.
    -   Potentially serve as a direct source for the prediction models.

## 3. Key Technologies & Components (Inferred)
-   **Programming Language**: Python
-   **Data Formats**: JSON (for configurations), CSV (for raw scraped data), Excel (for prediction output).
-   **Web Scraping Libraries**: (Not specified, but likely `requests`, `BeautifulSoup`, `Selenium`, or similar)
-   **Data Analysis/Prediction Libraries**: (Not specified, but could include `pandas`, `numpy`, `scikit-learn`, etc.)
-   **Database**: SQLite (proposed for the current data persistence task).

## 4. Project Goals
-   Automate the end-to-end data pipeline for sports prediction.
-   Ensure data accuracy and consistency through processes like team name mapping.
-   Provide a flexible and configurable system for adding and managing different leagues.
-   Generate actionable betting predictions.
-   Continuously improve data storage, processing, and prediction model capabilities.

## 5. Directory Structure Highlights
-   `data/raw/`: Stores raw scraped data in CSV format.
-   `data/processed/`: Stores final prediction Excel files.
-   `src/config/`: Contains configuration files like `leagues.json` and generated JSON databases.
-   `src/scrapers/`: Contains scraping logic and per-league Python config files.
-   `memory-bank/`: For project documentation and context (this set of files).
