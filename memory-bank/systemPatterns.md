# System Patterns: Automated Sports Betting Analysis System

## 1. Overall Architecture: Phased Data Pipeline
The system operates as a multi-stage pipeline, where data is progressively collected, transformed, and analyzed. Each stage typically involves dedicated scripts and produces intermediate outputs that feed into subsequent stages.

```mermaid
graph LR
    A[Initial Config: leagues.json] --> B(URL Extraction: url_extractor.py);
    B --> C[League JSON Configs: json_database/];
    C --> D(Team Name Mapping: team_name_mapper.py);
    D --> E(Config Overwrite: overwrite_config.py);
    E --> F[Master Config: config.py];
    F --> G(Config Splitting: split_config.py);
    G --> H[Per-League Py Configs: league_configs/];
    H --> I(URL Commenting: comment_urls.py);
    I --> J(Data Scraping: main.py);
    J --> K[Raw Data CSVs: data/raw/];
    K --> L(Database Ingestion - Current Task);
    L --> M[SQLite Database];
    M --> N(Prediction: testing_prediction.py);
    K --> N; # Current path, bypassing DB
    N --> O[Processed Data Excel: data/processed/];
```

## 2. Key Technical Decisions & Patterns

### 2.1. Configuration-Driven Processing
-   **Centralized Initial Configuration**: `leagues.json` acts as the master list of leagues and their primary URLs.
-   **Layered Configuration**: The system generates increasingly specific configuration files:
    1.  `leagues.json` (manual input)
    2.  Per-league JSON files in `json_database/` (automated, detailed URLs)
    3.  A temporary master `config.py` (Python dict format)
    4.  Per-league Python config files in `league_configs/` (final form for scrapers)
-   This layered approach allows for both broad overview and fine-grained control, though it also introduces complexity in managing the config transformation flow.

### 2.2. Modular Scripting
-   Each major step in the pipeline (URL extraction, team name mapping, scraping, prediction) is handled by a dedicated Python script (e.g., `url_extractor.py`, `team_name_mapper.py`, `main.py`, `testing_prediction.py`).
-   This promotes modularity and allows for individual components to be run or debugged separately.

### 2.3. Data Flow: Filesystem-Based
-   **Intermediate Data**: CSV files in `data/raw/` serve as the primary storage for scraped data before prediction.
-   **Configuration Data**: JSON and Python files store various stages of configuration.
-   **Output Data**: Excel files in `data/processed/` store the final predictions.
-   The current task aims to introduce a database (SQLite) to centralize and improve the persistence of scraped data.

### 2.4. Team Name Reconciliation
-   A critical pattern is the use of fuzzy matching (`team_name_mapper.py`) to reconcile discrepancies in team names from different sources. This is essential for data integrity.

### 2.5. Data Scrapers
-   The `main.py` script likely orchestrates various specialized scraping modules (e.g., `head_to_head.py`, `league_stats.py`, etc., inferred from `src/scrapers/` structure) to gather different types of data.
-   Scrapers rely on the per-league Python configuration files in `src/scrapers/league_configs/`.
-   **Dynamic On-Page Content Mapping**: For H2H stats, a pattern has been established where team names derived from the URL/title are dynamically mapped to team names extracted directly from the relevant section of the webpage. This ensures that scraped statistics (e.g., win percentages, goals) are correctly associated with the intended team, even if the order of teams in the URL differs from their visual presentation order on the page. This involves:
    -   Extracting a reference team name from a consistent location on the page (e.g., the "left" team in a comparison block).
    -   Comparing this on-page name (after sanitization) with the sanitized URL-derived team names.
    -   Setting dynamic dictionary keys based on this mapping to assign stats.

## 3. Component Relationships
-   **Configuration Scripts**: `url_extractor.py`, `overwrite_config.py`, `split_config.py`, `comment_urls.py` are interdependent and process configuration data sequentially.
-   **Data Processing Scripts**: `team_name_mapper.py` acts on configuration data to ensure consistency.
-   **Scraping Core**: `main.py` uses the finalized configurations to drive data collection.
-   **Prediction Core**: `testing_prediction.py` consumes the scraped data (currently CSVs, potentially database in future) to generate outputs.

## 4. Critical Implementation Paths
-   **Configuration Pipeline**: Ensuring the correct transformation and propagation of league and URL configurations through the various scripts and file formats is critical. Errors here can lead to incorrect scraping or missed data.
-   **Data Scraping Robustness**: Scrapers need to be resilient to changes in website structures and handle potential errors gracefully.
-   **Team Name Mapping Accuracy**: The effectiveness of the fuzzy matching for team names directly impacts data quality for H2H analysis and other relational data points.
-   **Data Flow to Prediction**: The path from raw data collection to the data format consumed by `testing_prediction.py` must be consistent. The introduction of a database will modify this path.
