# Technical Context: Automated Sports Betting Analysis System

## 1. Core Technologies
-   **Programming Language**: Python (versions not specified, assume Python 3.x).
-   **Primary Data Formats**:
    -   JSON: Used for initial league configurations (`leagues.json`), intermediate detailed configurations (`json_database/`), and checkpoint files (`checkpoints/`).
    -   CSV: Current primary format for storing raw scraped data (`data/raw/`).
    -   Python Dictionaries: Configurations are converted into Python dicts within scripts (e.g., `config.py`, files in `league_configs/`).
    -   Excel (.xlsx): Format for final prediction outputs (`data/processed/`).
-   **Proposed Database**: SQLite (for the current task of improving data persistence).
-   **Shell Scripting**: Bash scripts for process management and automation.

## 2. Key Libraries & Modules (Inferred & Mentioned)
-   **File System Operations**: Standard Python libraries like `os`, `glob`, `shutil` are likely used for managing files and directories.
-   **JSON Processing**: `json` module for configuration and checkpoint management.
-   **CSV Processing**: `csv` module or `pandas` for reading/writing CSV files.
-   **Web Scraping**:
    -   `requests` (for HTTP requests) with rate limiting and retry logic
    -   `BeautifulSoup4` (for parsing HTML/XML) with multiple fallback selectors
    -   Custom `RateLimitedScraper` class for managing request timing
-   **Fuzzy String Matching**: A library like `thefuzz` (formerly `fuzzywuzzy`) or `RapidFuzz` is likely used in `team_name_mapper.py`.
-   **Data Analysis & Prediction**:
    -   `pandas` is highly probable for data manipulation, especially if CSVs are heavily used.
    -   `numpy` for numerical operations.
    -   `scikit-learn` or other machine learning libraries for `testing_prediction.py`.
    -   Libraries for creating Excel files, such as `openpyxl` or `xlsxwriter`.
-   **Database Interaction (for SQLite)**: `sqlite3` (standard Python module).
-   **Logging**: Python's `logging` module for comprehensive operation tracking.
-   **Process Management**: `subprocess` module for shell script execution and process control.

## 3. Development Environment & Tools
-   **Editor/IDE**: Visual Studio Code is the current environment. Neovim is also mentioned in `how_to_use.md` for bulk formatting tasks.
-   **Version Control**: Git is implied by standard project structures (though no `.git` directory is explicitly listed in the initial file tree).
-   **Operating System**: The user's system is Linux.
-   **Python Environment Management**: Not specified, but `venv` or `conda` would be typical.
-   **Auto-formatting**: `how_to_use.md` mentions auto-formatting on save in Neovim, implying a formatter like Black, Yapf, or Ruff might be configured.
-   **Shell Environment**: Bash for automation scripts and process management.

## 4. Technical Constraints & Considerations
-   **Website Structure Dependency**: Scrapers are inherently dependent on the structure of target websites. Changes to these sites can break the scrapers, requiring updates.
-   **Rate Limiting/Blocking**: 
    -   **Critical Discovery**: Websites block after approximately 4 successful requests per session
    -   **Solution**: Process restart strategy with fresh Python processes between batches
    -   **Implementation**: Shell script management with random delays (5-10 seconds)
-   **Data Volume**: As more leagues and historical data are added, the volume of CSV files (and eventually database size) could grow, potentially impacting performance if not managed.
-   **Error Handling**: Robust error handling in scraping and data processing scripts is crucial for pipeline stability.
-   **Script Execution Order**: The current system relies on a specific execution order for its scripts due to data dependencies.
-   **Process Isolation**: Fresh processes required between batches to avoid rate limiting accumulation.

## 5. Tool Usage Patterns
-   **Command-Line Execution**: Scripts are designed to be run from the command line.
-   **Batch Processing**: 
    -   **Batch Size**: Exactly 4 URLs per batch to avoid rate limiting
    -   **Checkpoint System**: JSON-based progress tracking with resumption capability
    -   **Retry Logic**: Up to 3 attempts per URL with cooldown periods
-   **Configuration as Code**: League configurations are managed as code/data files (JSON, Python dicts).
-   **Filesystem as a Data Bus**: The filesystem is heavily used to pass data between different stages of the pipeline (e.g., JSON configs, CSV data files). The database integration aims to improve upon this for scraped data.
-   **Process Management**: Shell scripts coordinate Python process execution with proper timing and restart logic.

## 6. H2H Batch Scraping Architecture
-   **Core Components**:
    -   [`src/scrapers/h2h_batch_scraper.py`](src/scrapers/h2h_batch_scraper.py:1) - Main batch processing logic
    -   [`src/scrapers/h2h_automation.py`](src/scrapers/h2h_automation.py:1) - Single iteration runner
    -   [`run_h2h_automation.sh`](run_h2h_automation.sh:1) - Shell script coordinator
    -   [`run_h2h_batch.py`](run_h2h_batch.py:1) - User-friendly wrapper
-   **Checkpoint System**:
    -   Technical checkpoints: `checkpoints/{league}_h2h_checkpoint.json`
    -   Progress summaries: `checkpoints/{league}_h2h_progress.json`
    -   Automatic migration of legacy failed URLs to retry system
-   **Rate Limiting Strategy**:
    -   **Batch Size**: 4 URLs per execution
    -   **Process Restart**: Complete Python process restart between batches
    -   **Random Delays**: 5-10 seconds between iterations
    -   **Success Rate**: 100% for single-iteration batches

## 7. Enhanced H2H Scraping Features
-   **Fallback Selectors**: 8 different CSS selectors for each data point to handle website changes
-   **Team Name Normalization**:
    -   Serbian diacritic handling (č→c, ć→c, š→s, đ→dj, ž→z)
    -   City/regional identifier mapping (e.g., "Leskovac" in team names)
    -   Comprehensive team mapping database (87 Serbian team variations)
-   **Data Validation**: Automatic validation of extracted statistics with detailed logging
-   **Error Recovery**: Multiple retry attempts with exponential backoff and cooldown periods

## 8. MCP Tool Integration
-   **Web Scraping Debugging**:
    -   The `firecrawl_scrape` tool (from `github.com/mendableai/firecrawl-mcp-server`) was used to fetch raw HTML content of target H2H pages.
    -   This allowed for inspection of the live page structure to identify correct CSS selectors for team name extraction, especially when initial selectors failed.
    -   Using the `waitFor` parameter with `firecrawl_scrape` helped investigate if content (like recent match results) was dynamically loaded, confirming that for the specific "Past H2H" section, the initially loaded HTML was what the scraper had access to.
-   **Development Support**: MCP tools provided crucial debugging capabilities for understanding website structure and behavior.

## 9. Production Deployment Considerations
-   **Automation**: Fully automated system requiring no manual intervention
-   **Monitoring**: Comprehensive logging with progress tracking and error reporting
-   **Scalability**: Batch processing system can be applied to any league experiencing rate limiting
-   **Reliability**: Checkpoint system ensures no data loss and enables resumption after interruptions
-   **Maintenance**: Fallback selectors and retry mechanisms provide resilience to website changes
