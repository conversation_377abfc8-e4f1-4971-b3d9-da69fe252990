# Example configuration file for batch predictions
# Usage: python src/main.py --config predictions.yaml

matches:
  # English Premier League matches
  - league: "ENGLAND_PREMIER_LEAGUE"
    home: "Liverpool"
    away: "Arsenal"
  
  - league: "ENGLAND_PREMIER_LEAGUE"
    home: "Manchester City"
    away: "Chelsea"
  
  - league: "ENGLAND_PREMIER_LEAGUE"
    home: "Manchester United"
    away: "Tottenham"
  
  # Add more leagues if available
  # - league: "SPAIN_LA_LIGA"
  #   home: "Real Madrid"
  #   away: "Barcelona"
  
  # - league: "GERMANY_BUNDESLIGA"
  #   home: "Bayern Munich"
  #   away: "Borussia Dortmund"
