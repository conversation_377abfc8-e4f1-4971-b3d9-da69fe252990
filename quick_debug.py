#!/usr/bin/env python3
"""
Quick debug script to see model predictions vs Poisson calculations.
"""

import sys
import logging
from scipy.stats import poisson
import numpy as np

# Add src to path
sys.path.append('src')

# Setup logging to see debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def _calculate_poisson_win_probability(team_xg: float, opponent_xg: float, max_goals: int = 10) -> float:
    """Calculate the probability of a team winning based on expected goals using Poisson distribution."""
    try:
        win_prob = 0.0
        for team_goals in range(max_goals + 1):
            for opponent_goals in range(team_goals):  # team_goals > opponent_goals
                team_prob = poisson.pmf(team_goals, team_xg)
                opponent_prob = poisson.pmf(opponent_goals, opponent_xg)
                win_prob += team_prob * opponent_prob
        return max(0.01, min(0.98, win_prob))
    except Exception as e:
        print(f"Error calculating Poisson win probability: {str(e)}")
        return 0.33

def test_liverpool_arsenal():
    """Test the specific Liverpool vs Arsenal case."""
    home_xg, away_xg = 3.00, 1.75
    
    print("=" * 60)
    print("🔍 LIVERPOOL vs ARSENAL DEBUG")
    print("=" * 60)
    print(f"Expected Goals: {home_xg:.2f} - {away_xg:.2f}")
    
    # Calculate Poisson probabilities
    home_prob = _calculate_poisson_win_probability(home_xg, away_xg)
    away_prob = _calculate_poisson_win_probability(away_xg, home_xg)
    draw_prob = 1 - home_prob - away_prob
    
    # Ensure draw probability is non-negative
    draw_prob = max(0.05, draw_prob)
    
    # Normalize Poisson probabilities
    total_poisson = home_prob + draw_prob + away_prob
    poisson_probs = {
        "Home": home_prob / total_poisson,
        "Draw": draw_prob / total_poisson,
        "Away": away_prob / total_poisson
    }
    
    print(f"\n⚽ Poisson Probabilities (from xG):")
    print(f"   Home: {poisson_probs['Home']:.1%}")
    print(f"   Draw: {poisson_probs['Draw']:.1%}")
    print(f"   Away: {poisson_probs['Away']:.1%}")
    
    # Test what happens if model heavily favors draws (like in the actual results)
    # From the markdown: Draw 89.9%, Home 6.3%, Away 3.8%
    problematic_model_probs = {
        "Home": 0.063,
        "Draw": 0.899,
        "Away": 0.038
    }
    
    print(f"\n🤖 Problematic Model Probabilities (from actual results):")
    print(f"   Home: {problematic_model_probs['Home']:.1%}")
    print(f"   Draw: {problematic_model_probs['Draw']:.1%}")
    print(f"   Away: {problematic_model_probs['Away']:.1%}")
    
    # Current weights
    MODEL_WEIGHT = 0.4
    XG_WEIGHT = 0.6
    
    print(f"\n⚖️ Current Weights: MODEL={MODEL_WEIGHT}, XG={XG_WEIGHT}")
    
    # Blend with current weights
    blended_probs = {
        "Home": MODEL_WEIGHT * problematic_model_probs["Home"] + XG_WEIGHT * poisson_probs["Home"],
        "Draw": MODEL_WEIGHT * problematic_model_probs["Draw"] + XG_WEIGHT * poisson_probs["Draw"],
        "Away": MODEL_WEIGHT * problematic_model_probs["Away"] + XG_WEIGHT * poisson_probs["Away"]
    }
    
    # Normalize blended probabilities
    total = sum(blended_probs.values())
    final_probs = {k: v / total for k, v in blended_probs.items()}
    
    print(f"\n🎯 Blended Final Probabilities:")
    print(f"   Home: {final_probs['Home']:.1%}")
    print(f"   Draw: {final_probs['Draw']:.1%}")
    print(f"   Away: {final_probs['Away']:.1%}")
    
    # Determine prediction
    max_prob = max(final_probs.values())
    prediction = [k for k, v in final_probs.items() if v == max_prob][0]
    print(f"\n🏆 Final Prediction: {prediction}")
    
    # Check if this matches the actual problematic result
    if prediction == "Draw" and final_probs["Draw"] > 0.8:
        print("❌ PROBLEM REPRODUCED: Model heavily favors draws despite clear xG advantage")
        print("💡 SOLUTION: Need to increase XG_WEIGHT or fix model predictions")
    else:
        print("✅ This combination would produce a reasonable result")
    
    # Test better weights
    print(f"\n🔧 TESTING BETTER WEIGHTS:")
    better_weights = [
        (0.1, 0.9),  # 10% model, 90% xG
        (0.2, 0.8),  # 20% model, 80% xG
        (0.3, 0.7),  # 30% model, 70% xG
    ]
    
    for model_w, xg_w in better_weights:
        blended = {
            "Home": model_w * problematic_model_probs["Home"] + xg_w * poisson_probs["Home"],
            "Draw": model_w * problematic_model_probs["Draw"] + xg_w * poisson_probs["Draw"],
            "Away": model_w * problematic_model_probs["Away"] + xg_w * poisson_probs["Away"]
        }
        
        total = sum(blended.values())
        final = {k: v / total for k, v in blended.items()}
        
        max_prob = max(final.values())
        pred = [k for k, v in final.items() if v == max_prob][0]
        
        print(f"   Weights {model_w:.1f}/{xg_w:.1f}: Home {final['Home']:.1%}, Draw {final['Draw']:.1%}, Away {final['Away']:.1%} → {pred}")

if __name__ == "__main__":
    test_liverpool_arsenal()
