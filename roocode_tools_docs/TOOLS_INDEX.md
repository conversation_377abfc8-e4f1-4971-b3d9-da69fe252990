# Roocode Tools Documentation Index

*Generated: June 3, 2025 - Complete documentation for all Roocode tools*

*Total tools: 17 | Successfully scraped: 17 | Failed: 0*

---

## 📋 Complete Tool Reference

This directory contains detailed documentation for **every Roocode tool**, scraped directly from the official documentation. Use these files when <PERSON><PERSON> gets stuck or needs clarification on specific tool usage.

---

## 📁 Successfully Scraped Tools

### 📖 Read Tools
*Help Roo understand your code and project structure*

- **[read_file](./individual_tools/read_file.md)** - `read_file` - Examine file contents and understand code structure
- **[list_files](./individual_tools/list_files.md)** - `list_files` - Map project file structure and discover files
- **[list_code_definition_names](./individual_tools/list_code_definition_names.md)** - `list_code_definition_names` - Create structural maps of code definitions

### 🔍 Search Tools  
*Help Roo find patterns and functionality across codebases*

- **[search_files](./individual_tools/search_files.md)** - `search_files` - Find patterns across multiple files using regex
- **[codebase_search](./individual_tools/codebase_search.md)** - `codebase_search` - Perform semantic searches across indexed codebases

### ✏️ Edit Tools
*Help Roo make precise changes to code*

- **[apply_diff](./individual_tools/apply_diff.md)** - `apply_diff` - Make precise, surgical changes to code
- **[insert_content](./individual_tools/insert_content.md)** - `insert_content` - Add content without modifying existing lines
- **[search_and_replace](./individual_tools/search_and_replace.md)** - `search_and_replace` - Find and replace text/regex patterns
- **[write_to_file](./individual_tools/write_to_file.md)** - `write_to_file` - Create new files or completely rewrite existing ones

### 🌐 Browser Tools
*Web automation capabilities*

- **[browser_action](./individual_tools/browser_action.md)** - `browser_action` - Automate browser interactions for web testing

### ⚡ Command Tools
*System command execution*

- **[execute_command](./individual_tools/execute_command.md)** - `execute_command` - Run system commands and programs

### 🔗 MCP Tools
*External tool integration via Model Context Protocol*

- **[use_mcp_tool](./individual_tools/use_mcp_tool.md)** - `use_mcp_tool` - Use specialized external tools through MCP servers
- **[access_mcp_resource](./individual_tools/access_mcp_resource.md)** - `access_mcp_resource` - Access external data sources via MCP

### 🔄 Workflow Tools
*Conversation and task management*

- **[ask_followup_question](./individual_tools/ask_followup_question.md)** - `ask_followup_question` - Gather additional information from users
- **[attempt_completion](./individual_tools/attempt_completion.md)** - `attempt_completion` - Signal task completion and present results
- **[switch_mode](./individual_tools/switch_mode.md)** - `switch_mode` - Change operational modes for specialized tasks
- **[new_task](./individual_tools/new_task.md)** - `new_task` - Create subtasks for complex operations

---

## 🔧 Tool Categories Quick Reference

| Category | Count | Purpose |
|----------|-------|---------|
| **Read Tools** | 3 | Code exploration and analysis |
| **Search Tools** | 2 | Pattern and semantic searching |
| **Edit Tools** | 4 | Code changes and file manipulation |
| **Browser Tools** | 1 | Web automation and testing |
| **Command Tools** | 1 | System command execution |
| **MCP Tools** | 2 | External tool integration |
| **Workflow Tools** | 4 | Task and conversation management |

---

## 📖 How to Use This Documentation

### 🎯 When Roo Gets Stuck
1. **Identify the problematic tool** from error messages or context
2. **Reference the specific tool documentation** using the links above
3. **Check parameters and examples** in the tool's documentation
4. **Look for common errors and solutions** in the tool's error handling section

### 📋 Each Tool Documentation Contains:
- **Overview**: Purpose and functionality description
- **Parameters**: Required and optional parameters with types
- **Usage Examples**: Practical code examples and use cases
- **Best Practices**: Recommended usage patterns and tips
- **Error Handling**: Common errors and troubleshooting solutions
- **Related Tools**: Connected functionality and workflow patterns

### 🔗 Quick Access Patterns:
- **File Operations**: `read_file` → `apply_diff` → `attempt_completion`
- **Code Discovery**: `list_files` → `read_file` → `codebase_search`
- **Search & Replace**: `search_files` → `search_and_replace` → `attempt_completion`
- **Information Gathering**: `ask_followup_question` → `read_file` → analysis
- **Task Management**: `new_task` → `switch_mode` → tool execution

---

## 📊 Documentation Statistics

- **Total Content**: 234+ KB of detailed documentation
- **Average per Tool**: ~13,800 characters
- **Source**: Official Roocode documentation (docs.roocode.com)
- **Coverage**: 100% of available tools documented
- **Last Updated**: June 3, 2025

---

## 🚀 Advanced Usage Tips

### Mode-Based Tool Access
- **Code Mode**: Full access to all tools
- **Ask Mode**: Limited to read tools and information gathering
- **Architect Mode**: Design-focused tools with limited execution
- **Custom Modes**: Configurable tool access for specialized workflows

### Tool Orchestration
Tools are designed to work together in sequences:
1. **Discovery Phase**: `list_files` → `read_file` → `list_code_definition_names`
2. **Analysis Phase**: `search_files` → `codebase_search` → pattern identification
3. **Modification Phase**: `apply_diff` → `insert_content` → `write_to_file`
4. **Verification Phase**: `execute_command` → testing → `attempt_completion`

### Error Recovery
- Each tool has built-in error handling and recovery strategies
- Failed operations can often be retried with adjusted parameters
- Use `ask_followup_question` to clarify requirements when stuck

---

*This comprehensive documentation was scraped from the official Roocode documentation to provide complete tool reference for troubleshooting and optimization.*
