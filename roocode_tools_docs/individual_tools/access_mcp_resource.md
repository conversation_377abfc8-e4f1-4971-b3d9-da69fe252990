# access_mcp_resource Tool Documentation

*Tool: access_mcp_resource*
*Source: https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource*
*Scraped: 2025-06-03 14:39:58*
*Content length: 12301 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * access_mcp_resource


On this page
# access_mcp_resource
The `access_mcp_resource` tool retrieves data from resources exposed by connected Model Context Protocol (MCP) servers. It allows Roo to access files, API responses, documentation, or system information that provides additional context for tasks.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `server_name` (required): The name of the MCP server providing the resource
  * `uri` (required): The URI identifying the specific resource to access


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#what-it-does "Direct link to What It Does")
This tool connects to MCP servers and fetches data from their exposed resources. Unlike `use_mcp_tool` which executes actions, this tool specifically retrieves information that serves as context for tasks.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#when-is-it-used "Direct link to When is it used?")
  * When Roo needs additional context from external systems
  * When Roo needs to access domain-specific data from specialized MCP servers
  * When Roo needs to retrieve reference documentation hosted by MCP servers
  * When Roo needs to integrate real-time data from external APIs via MCP


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#key-features "Direct link to Key Features")
  * Retrieves both text and image data from MCP resources
  * Requires user approval before executing resource access
  * Uses URI-based addressing to precisely identify resources
  * Integrates with the Model Context Protocol SDK
  * Displays resource content appropriately based on content type
  * Supports timeouts for reliable network operations
  * Handles server connection states (connected, connecting, disconnected)
  * Discovers available resources from connected servers
  * Processes structured response data with metadata
  * Handles image content special rendering


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#limitations "Direct link to Limitations")
  * Depends on external MCP servers being available and connected
  * Limited to the resources provided by connected servers
  * Cannot access resources from disabled servers
  * Network issues can affect reliability and performance
  * Resource access subject to configured timeouts
  * URI formats are determined by the specific MCP server implementation
  * No offline or cached resource access capabilities


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#how-it-works "Direct link to How It Works")
When the `access_mcp_resource` tool is invoked, it follows this process:
  1. **Connection Validation** :
     * Verifies that an MCP hub is available and initialized
     * Confirms the specified server exists in the connection list
     * Checks if the server is disabled (returns an error if it is)
  2. **User Approval** :
     * Presents the resource access request to the user for approval
     * Provides server name and resource URI for user verification
     * Proceeds only if the user approves the resource access
  3. **Resource Request** :
     * Uses the Model Context Protocol SDK to communicate with servers
     * Makes a `resources/read` request to the server through the MCP hub
     * Applies configured timeouts to prevent hanging on unresponsive servers
  4. **Response Processing** :
     * Receives a structured response with metadata and content arrays
     * Processes text content for display to the user
     * Handles image data specially for appropriate display
     * Returns the processed resource data to Roo for use in the current task


## Resource Types[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#resource-types "Direct link to Resource Types")
MCP servers can provide two main types of resources:
  1. **Standard Resources** :
     * Fixed resources with specific URIs
     * Defined name, description, and MIME type
     * Direct access without parameters
     * Typically represent static data or real-time information
  2. **Resource Templates** :
     * Parameterized resources with placeholder values in URIs
     * Allow dynamic resource generation based on provided parameters
     * Can represent queries or filtered views of data
     * More flexible but require additional URI formatting


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#examples-when-used "Direct link to Examples When Used")
  * When helping with API development, Roo retrieves endpoint specifications from MCP resources to ensure correct implementation.
  * When assisting with data visualization, Roo accesses current data samples from connected MCP servers.
  * When working in specialized domains, Roo retrieves technical documentation to provide accurate guidance.
  * When generating industry-specific code, Roo references compliance requirements from documentation resources.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#usage-examples "Direct link to Usage Examples")
Accessing current weather data:
```
<access_mcp_resource><server_name>weather-server</server_name><uri>weather://san-francisco/current</uri></access_mcp_resource>
```

Retrieving API documentation:
```
<access_mcp_resource><server_name>api-docs</server_name><uri>docs://payment-service/endpoints</uri></access_mcp_resource>
```

Accessing domain-specific knowledge:
```
<access_mcp_resource><server_name>knowledge-base</server_name><uri>kb://medical/terminology/common</uri></access_mcp_resource>
```

Fetching system configuration:
```
<access_mcp_resource><server_name>infra-monitor</server_name><uri>config://production/database</uri></access_mcp_resource>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/access-mcp-resource.md)
Last updated on **May 27, 2025**
[ PreviousTool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)[Nextapply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#how-it-works)
  * [Resource Types](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#resource-types)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Faccess-mcp-resource)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



