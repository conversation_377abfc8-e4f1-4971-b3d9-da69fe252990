# apply_diff Tool Documentation

*Tool: apply_diff*
*Source: https://docs.roocode.com/advanced-usage/available-tools/apply-diff*
*Scraped: 2025-06-03 14:27:45*
*Content length: 11644 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-dark.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/getting-started/installing)
  * [Features](https://docs.roocode.com/features/api-configuration-profiles)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/providers/anthropic)
  * [FAQ](https://docs.roocode.com/faq)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/community/)
  * [Update Notes](https://docs.roocode.com/update-notes/)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * apply_diff


On this page
# apply_diff
The `apply_diff` tool makes precise, surgical changes to files by specifying exactly what content to replace. It uses a sophisticated strategy for finding and applying changes while maintaining proper code formatting and structure.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The path of the file to modify relative to the current working directory.
  * `diff` (required): The search/replace block defining the changes using a format specific to the active diff strategy.
  * `start_line` (optional): A hint for where the search content begins. _Note: This top-level parameter appears unused by the current main strategy, which relies on`:start_line:` within the diff content._
  * `end_line` (optional): A hint for where the search content ends. _Note: This top-level parameter appears unused by the current main strategy._


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#what-it-does "Direct link to What It Does")
This tool applies targeted changes to existing files using fuzzy matching guided by line number hints to locate and replace content precisely. Unlike simple search and replace, it identifies the exact block for replacement based on the provided content and location hints.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to make precise changes to existing code without rewriting entire files.
  * When refactoring specific sections of code while maintaining surrounding context.
  * When fixing bugs in existing code with surgical precision.
  * When implementing feature enhancements that modify only certain parts of a file.


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#key-features "Direct link to Key Features")
  * Uses fuzzy matching (Levenshtein distance on normalized strings) guided by a `:start_line:` hint, with configurable confidence thresholds (typically 0.8-1.0).
  * Provides context around matches using `BUFFER_LINES` (default 40).
  * Performs a middle-out search within a configurable context window (`bufferLines`) around the hinted start line.
  * Preserves code formatting and indentation passively by replacing exact blocks.
  * Shows changes in a diff view for user review and editing before applying.
  * Tracks consecutive errors per file (`consecutiveMistakeCountForApplyDiff`) to prevent repeated failures.
  * Validates file access against `.rooignore` rules.
  * Handles multi-line edits effectively.


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#limitations "Direct link to Limitations")
  * Works best with unique, distinctive code sections for reliable identification.
  * Performance can vary with very large files or highly repetitive code patterns.
  * Fuzzy matching might occasionally select incorrect locations if content is ambiguous.
  * Each diff strategy has specific format requirements.
  * Complex edits might require careful strategy selection or manual review.


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#how-it-works "Direct link to How It Works")
When the `apply_diff` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates required `path` and `diff` parameters.
  2. **RooIgnore Check** : Validates if the target file path is allowed by `.rooignore` rules.
  3. **File Analysis** : Loads the target file content.
  4. **Match Finding** : Uses a fuzzy matching algorithm (Levenshtein on normalized strings) guided by the `:start_line:` hint within a context window (`BUFFER_LINES`), searching middle-out to locate the target content based on the confidence threshold.
  5. **Change Preparation** : Generates the proposed changes by replacing the identified block.
  6. **User Interaction** : 
     * Displays the changes in a diff view.
     * Allows the user to review and potentially edit the proposed changes.
     * Waits for user approval or rejection.
  7. **Change Application** : If approved, applies the changes (potentially including user edits) to the file.
  8. **Error Handling** : If errors occur (e.g., match failure, partial application), increments the `consecutiveMistakeCountForApplyDiff` for the file and reports the failure type.
  9. **Feedback** : Returns the result, including any user feedback or error details.


## Diff Format Requirements[​](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#diff-format-requirements "Direct link to Diff Format Requirements")
The `<diff>` parameter requires a specific format supporting one or more changes in a single request. Each change block requires a line number hint for the original content.
  * **Requires** : Exact match for the `SEARCH` block content (within the fuzzy threshold), including whitespace and indentation. The `:start_line:` number hint is mandatory within each block. The `:end_line:` hint is optional (but supported by the parser). Markers like `<<<<<<<` within the file's content must be escaped (`\\`) in the SEARCH block.


Example format for the `<diff>` block:
```
<<<<<<< SEARCH:start_line:10:end_line:12-------  // Old calculation logic  const result = value * 0.9;  return result;=======  // Updated calculation logic with logging  console.log(`Calculating for value: ${value}`);  const result = value * 0.95; // Adjusted factor  return result;>>>>>>> REPLACE<<<<<<< SEARCH:start_line:25:end_line:25-------  const defaultTimeout = 5000;=======  const defaultTimeout = 10000; // Increased timeout>>>>>>> REPLACE
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/apply-diff.md)
Last updated on **May 27, 2025**
[ Previousaccess_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)[Nextask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#how-it-works)
  * [Diff Format Requirements](https://docs.roocode.com/advanced-usage/available-tools/apply-diff#diff-format-requirements)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fapply-diff)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



