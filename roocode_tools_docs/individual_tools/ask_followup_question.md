# ask_followup_question Tool Documentation

*Tool: ask_followup_question*
*Source: https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question*
*Scraped: 2025-06-03 16:43:04*
*Content length: 15374 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-dark.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/getting-started/installing)
  * [Features](https://docs.roocode.com/features/api-configuration-profiles)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/providers/anthropic)
  * [FAQ](https://docs.roocode.com/faq)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/community/)
  * [Update Notes](https://docs.roocode.com/update-notes/)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * ask_followup_question


On this page
# ask_followup_question
The `ask_followup_question` tool enables interactive communication by asking specific questions to gather additional information needed to complete tasks effectively.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `question` (required): The specific question to ask the user
  * `follow_up` (optional): A list of 2-4 suggested answers that help guide user responses, each within `<suggest>` tags


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#what-it-does "Direct link to What It Does")
This tool creates a conversational interface between Roo and the user, allowing for gathering clarification, additional details, or user preferences when facing ambiguities or decision points. Each question can include suggested responses to streamline the interaction.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#when-is-it-used "Direct link to When is it used?")
  * When critical information is missing from the original request
  * When Roo needs to choose between multiple valid implementation approaches
  * When technical details or preferences are required to proceed
  * When Roo encounters ambiguities that need resolution
  * When additional context would significantly improve the solution quality


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#key-features "Direct link to Key Features")
  * Provides a structured way to gather specific information without breaking workflow
  * Includes suggested answers to reduce user typing and guide responses
  * Maintains conversation history and context across interactions
  * Supports responses containing images and code snippets
  * Available in all modes as part of the "always available" tool set
  * Enables direct user guidance on implementation decisions
  * Formats responses with `<answer>` tags to distinguish them from regular conversation
  * Resets consecutive error counter when used successfully


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#limitations "Direct link to Limitations")
  * Limited to asking one specific question per tool use
  * Presents suggestions as selectable options in the UI
  * Cannot force structured responses – users can still respond freely
  * Excessive use can slow down task completion and create a fragmented experience
  * Suggested answers must be complete, with no placeholders requiring user edits
  * No built-in validation for user responses
  * Contains no mechanism to enforce specific answer formats


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#how-it-works "Direct link to How It Works")
When the `ask_followup_question` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates the required `question` parameter and checks for optional suggestions
     * Ensures question text is provided
     * Parses any suggested answers from the `follow_up` parameter using the `fast-xml-parser` library
     * Normalizes suggestions into an array format even if there's only one suggestion
  2. **JSON Transformation** : Converts the XML structure into a standardized JSON format for UI display
```
{ question:"User's question here", suggest:[{ answer:"Suggestion 1"},{ answer:"Suggestion 2"}]}
```

  3. **UI Integration** :
     * Passes the JSON structure to the UI layer via the `ask("followup", ...)` method
     * Displays selectable suggestion buttons to the user in the interface
     * Creates an interactive experience for selecting or typing a response
  4. **Response Collection and Processing** :
     * Captures user text input and any images included in the response
     * Wraps user responses in `<answer>` tags when returning to the assistant
     * Preserves any images included in the user's response
     * Maintains the conversational context by adding the response to the history
     * Resets the consecutive error counter when the tool is used successfully
  5. **Error Handling** :
     * Tracks consecutive mistakes using a counter
     * Resets the counter when the tool is used successfully
     * Provides specific error messages: 
       * For missing parameters: "Missing required parameter 'question'"
       * For XML parsing: "Failed to parse operations: [error message]"
       * For invalid format: "Invalid operations xml format"
     * Contains safeguards to prevent tool execution when required parameters are missing
     * Increments consecutive mistake count when errors occur


## Workflow Sequence[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#workflow-sequence "Direct link to Workflow Sequence")
The question-answer cycle follows this sequence:
  1. **Information Gap Recognition** : Roo identifies missing information needed to proceed
  2. **Specific Question Creation** : Roo formulates a clear, targeted question
  3. **Suggestion Development** : Roo creates relevant suggested answers (optional but recommended)
  4. **Tool Invocation** : Assistant invokes the tool with question and optional suggestions
  5. **UI Presentation** : Question and suggestions are displayed to the user as interactive elements
  6. **User Response** : The user selects a suggestion or provides a custom answer
  7. **Message Handling** : System handles both partial and complete messages 
     * For streaming responses, processes chunks as they arrive
     * For complete messages, processes the entire response at once
     * Maintains state consistency regardless of message chunking
  8. **Response Processing** : System wraps the response in `<answer>` tags and preserves images
  9. **Context Integration** : Response is added to the conversation history
  10. **Task Continuation** : Roo proceeds with the task using the new information


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#examples-when-used "Direct link to Examples When Used")
  * When developing a web application, Roo might ask about preferred styling frameworks (Bootstrap, Tailwind, custom CSS)
  * When creating an API, Roo might ask about authentication methods (JWT, OAuth, API keys)
  * When refactoring code, Roo might ask about prioritizing performance vs. readability
  * When setting up a database, Roo might ask about specific schema design preferences
  * When creating a custom feature, Roo might ask about specific behavior expectations
  * When troubleshooting errors, Roo might ask about specific environment details


## Response Format[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#response-format "Direct link to Response Format")
When a user responds to a question, the response is formatted with `<answer>` tags for clarity and consistency. This format helps clearly separate the user's answer from other conversation elements.
Example response format:
```
<answer>Use Tailwind CSS for utility-first styling with maximum flexibility</answer>
```

Key characteristics of the response format:
  * Surrounded by `<answer>` tags
  * Preserves formatting of the original response
  * Maintains images if included in the response
  * Returns the complete text, even for long responses


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#usage-examples "Direct link to Usage Examples")
Asking about implementation preferences:
```
<ask_followup_question><question>Which styling approach would you prefer for this web application?</question><follow_up><suggest>Use Bootstrap for rapid development with consistent components</suggest><suggest>Use Tailwind CSS for utility-first styling with maximum flexibility</suggest><suggest>Use vanilla CSS with custom styling for complete control and minimal dependencies</suggest></follow_up></ask_followup_question>
```

Requesting technical clarification:
```
<ask_followup_question><question>What database should this application use for storing user data?</question><follow_up><suggest>MongoDB for flexible schema and document-based storage</suggest><suggest>PostgreSQL for relational data with strong consistency guarantees</suggest><suggest>Firebase for real-time updates and simplified backend management</suggest><suggest>SQLite for lightweight local storage without external dependencies</suggest></follow_up></ask_followup_question>
```

Addressing ambiguity in requirements:
```
<ask_followup_question><question>How should the application handle user authentication?</question><follow_up><suggest>Implement email/password authentication with account verification</suggest><suggest>Use social login providers (Google, GitHub, etc.) for quick signup</suggest><suggest>Implement both email/password and social login options</suggest></follow_up></ask_followup_question>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/ask-followup-question.md)
Last updated on **May 27, 2025**
[ Previousapply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)[Nextattempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#how-it-works)
  * [Workflow Sequence](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#workflow-sequence)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#examples-when-used)
  * [Response Format](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#response-format)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fask-followup-question)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



