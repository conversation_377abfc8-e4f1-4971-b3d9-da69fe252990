# attempt_completion Tool Documentation

*Tool: attempt_completion*
*Source: https://docs.roocode.com/advanced-usage/available-tools/attempt-completion*
*Scraped: 2025-06-03 16:43:10*
*Content length: 15864 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * attempt_completion


On this page
# attempt_completion
The `attempt_completion` tool signals that Roo believes a task is complete and presents results to the user. It provides a summary of what was accomplished, optionally includes a command to demonstrate the result, and supports continued refinement through user feedback.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `result` (required): The final result description summarizing what was accomplished
  * `command` (optional): A CLI command to execute to demonstrate the result


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#what-it-does "Direct link to What It Does")
This tool marks the end of a task by presenting a final summary of what was accomplished. It signals to both the user and the system that the current task is complete. When a command is provided, it can also demonstrate the result through a live execution.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#when-is-it-used "Direct link to When is it used?")
  * When Roo believes the user's task has been completed
  * When summarizing what changes or improvements were made
  * When demonstrating the current result with a command execution
  * When providing a checkpoint for user feedback on the solution
  * When transitioning from one phase of work to potential refinements


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#key-features "Direct link to Key Features")
  * Provides a clear signal that Roo believes the task is complete
  * Summarizes accomplishments in a concise message
  * Optionally demonstrates results through command execution
  * Enables user feedback for further refinements
  * Displays results in a special UI format distinct from regular messages
  * Captures task completion telemetry for system analytics
  * Maintains a structured conversation flow by providing checkpoints
  * Supports subtask completion within larger workflows
  * Ensures users receive a clear summary of what was done
  * Available in all modes as part of the "always available" tool group


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#limitations "Direct link to Limitations")
  * Should not be used until previous tool uses are confirmed successful (guideline, not enforced)
  * Limited to a single command for result demonstration
  * Cannot present multiple command options
  * Commands require user approval before execution
  * Limited to demonstrating results that can be shown via CLI commands
  * Cannot be used for partial task completion or progress updates
  * Result formatting strips XML closing tags through internal processing


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#how-it-works "Direct link to How It Works")
When the `attempt_completion` tool is invoked, it follows this process:
  1. **Safety Consideration** (guideline, not enforced):
     * The AI is instructed to confirm previous tool uses were successful
     * This is a best practice rather than a programmatically enforced mechanism
  2. **Result Presentation** :
     * Displays the completion message to the user in a special "completion_result" UI format
     * Removes XML closing tags from the result text using the `removeClosingTag` function
     * Presents the result differently than regular messages for visual distinction
  3. **Command Execution** (if provided):
     * Requests user approval before executing the command
     * Only executes if the user approves
     * Executes the command using the system's command execution functionality
     * Shows the result of the command to the user
  4. **Feedback Collection** :
     * Waits for user feedback on the completion result
     * Processes this feedback and returns it to the AI
     * Enables continued refinement based on user input
  5. **Task Completion and Continuation** :
     * Signals the task as completed in the system
     * Captures telemetry data for the completed task
     * For subtasks, offers to finish the subtask and resume the parent task
     * Supports continued conversation through the feedback mechanism
  6. **Implementation Integration** :
     * Tool results are parsed through the system's parsing mechanism in `parse-assistant-message.ts`
     * The tool is part of the "ALWAYS_AVAILABLE_TOOLS" constant, making it available in all modes


## Result Formatting Guidelines[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#result-formatting-guidelines "Direct link to Result Formatting Guidelines")
The result message should follow these guidelines:
  * Clearly communicate what was accomplished
  * Be concise but complete
  * Focus on the value delivered to the user
  * Avoid unnecessary pleasantries or filler text
  * Maintain a professional, straightforward tone
  * Present information in a way that's easy to scan and understand
  * Acknowledge that the user may provide feedback for further refinements


Note: The system automatically strips XML closing tags from the result text through the `removeClosingTag` function.
## Command Selection Guidelines[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#command-selection-guidelines "Direct link to Command Selection Guidelines")
When including a command, follow these guidelines:
  * Choose commands that visually demonstrate the result
  * Prefer commands that show the user what was created or modified
  * Examples include: 
    * `open index.html` to display a created website
    * `npm start` to launch a development server
    * `python app.py` to run a created application
  * Avoid commands that merely print text (like `echo` or `cat`)
  * Remember that commands require user approval before execution
  * Ensure the command is valid for the user's operating system


## Feedback and UI Representation[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#feedback-and-ui-representation "Direct link to Feedback and UI Representation")
The `attempt_completion` tool has a unique feedback mechanism:
  * Results appear in a special "completion_result" UI format distinct from regular messages
  * The system waits for user feedback after presenting the result
  * Feedback is processed and returned to Roo for further refinements
  * This creates an iterative improvement cycle rather than ending the conversation
  * The UI includes special elements for providing feedback
  * Results serve as clear checkpoints in the conversation flow


When used within subtasks:
  * The system offers to finish the subtask and resume the parent task
  * If approved, the subtask is completed with a summary
  * The parent task resumes with context from the completed subtask
  * This enables complex, nested workflows while maintaining context


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#examples-when-used "Direct link to Examples When Used")
  * When creating a website, Roo presents the solution and uses `open index.html` to show the result. The user provides feedback, and Roo refines the design.
  * When implementing a feature, Roo summarizes the changes and their impact. The user requests additional test coverage, and Roo continues the task.
  * When fixing bugs, Roo explains what was fixed and how the solution addresses the root cause. The user confirms the fix works.
  * When setting up a development environment, Roo confirms all components are installed. The user requests additional configuration, and Roo continues.
  * When creating a data processing script, Roo summarizes the functionality. The user suggests performance improvements, and Roo optimizes the code.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#usage-examples "Direct link to Usage Examples")
Completing a task with just a result summary:
```
<attempt_completion><result>I've updated the CSS for the navigation bar to match the design mockup. The changes include:1. Updated color scheme to use the new brand colors2. Added responsive behavior for mobile devices3. Fixed the alignment issues in the dropdown menus</result></attempt_completion>
```

Completing a task with a result and demonstration command:
```
<attempt_completion><result>I've created a simple React application with the following features:1. A responsive landing page with the requested hero section2. A working contact form with validation3. Navigation between all three requested pages4. Styling that matches the provided design mockup</result><command>npm start</command></attempt_completion>
```

Completing a Python script task with execution:
```
<attempt_completion><result>I've created a data processing script that:1. Reads the CSV files from the data directory2. Cleans and normalizes the data as requested3. Generates the summary statistics in JSON format4. Outputs the processed data to the output directory</result><command>python process_data.py</command></attempt_completion>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/attempt-completion.md)
Last updated on **May 27, 2025**
[ Previousask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)[Nextbrowser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#how-it-works)
  * [Result Formatting Guidelines](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#result-formatting-guidelines)
  * [Command Selection Guidelines](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#command-selection-guidelines)
  * [Feedback and UI Representation](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#feedback-and-ui-representation)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fattempt-completion)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



