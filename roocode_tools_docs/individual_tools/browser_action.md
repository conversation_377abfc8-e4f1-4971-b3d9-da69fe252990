# browser_action Tool Documentation

*Tool: browser_action*
*Source: https://docs.roocode.com/advanced-usage/available-tools/browser-action*
*Scraped: 2025-06-03 14:39:50*
*Content length: 14549 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/browser-action#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/browser-action)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * browser_action


On this page
# browser_action
The `browser_action` tool enables web automation and interaction via a Puppeteer-controlled browser. It allows Roo to launch browsers, navigate to websites, click elements, type text, and scroll pages with visual feedback through screenshots.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `action` (required): The action to perform: 
    * `launch`: Start a new browser session at a URL
    * `click`: Click at specific x,y coordinates
    * `type`: Type text via the keyboard
    * `scroll_down`: Scroll down one page height
    * `scroll_up`: Scroll up one page height
    * `close`: End the browser session
  * `url` (optional): The URL to navigate to when using the `launch` action
  * `coordinate` (optional): The x,y coordinates for the `click` action (e.g., "450,300")
  * `text` (optional): The text to type when using the `type` action


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#what-it-does "Direct link to What It Does")
This tool creates an automated browser session that Roo can control to navigate websites, interact with elements, and perform tasks that require browser automation. Each action provides a screenshot of the current state, enabling visual verification of the process.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to interact with web applications or websites
  * When testing user interfaces or web functionality
  * When capturing screenshots of web pages
  * When demonstrating web workflows visually


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#key-features "Direct link to Key Features")
  * Provides visual feedback with screenshots after each action and captures console logs
  * Supports complete workflows from launching to page interaction to closing
  * Enables precise interactions via coordinates, keyboard input, and scrolling
  * Maintains consistent browser sessions with intelligent page loading detection
  * Operates in two modes: local (isolated Puppeteer instance) or remote (connects to existing Chrome)
  * Handles errors gracefully with automatic session cleanup and detailed messages
  * Optimizes visual output with support for various formats and quality settings
  * Tracks interaction state with position indicators and action history


## Browser Modes[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#browser-modes "Direct link to Browser Modes")
The tool operates in two distinct modes:
### Local Browser Mode (Default)[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#local-browser-mode-default "Direct link to Local Browser Mode \(Default\)")
  * Downloads and manages a local Chromium instance through Puppeteer
  * Creates a fresh browser environment with each launch
  * No access to existing user profiles, cookies, or extensions
  * Consistent, predictable behavior in a sandboxed environment
  * Completely closes the browser when the session ends


### Remote Browser Mode[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#remote-browser-mode "Direct link to Remote Browser Mode")
  * Connects to an existing Chrome/Chromium instance running with remote debugging enabled
  * Can access existing browser state, cookies, and potentially extensions
  * Faster startup as it reuses an existing browser process
  * Supports connecting to browsers in Docker containers or on remote machines
  * Only disconnects (doesn't close) from the browser when session ends
  * Requires Chrome to be running with remote debugging port open (typically port 9222)


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#limitations "Direct link to Limitations")
  * While the browser is active, only `browser_action` tool can be used
  * Browser coordinates are viewport-relative, not page-relative
  * Click actions must target visible elements within the viewport
  * Browser sessions must be explicitly closed before using other tools
  * Browser window has configurable dimensions (default 900x600)
  * Cannot directly interact with browser DevTools
  * Browser sessions are temporary and not persistent across Roo restarts
  * Works only with Chrome/Chromium browsers, not Firefox or Safari
  * Local mode has no access to existing cookies; remote mode requires Chrome with debugging enabled


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#how-it-works "Direct link to How It Works")
When the `browser_action` tool is invoked, it follows this process:
  1. **Action Validation and Browser Management** :
     * Validates the required parameters for the requested action
     * For `launch`: Initializes a browser session (either local Puppeteer instance or remote Chrome)
     * For interaction actions: Uses the existing browser session
     * For `close`: Terminates or disconnects from the browser appropriately
  2. **Page Interaction and Stability** :
     * Ensures pages are fully loaded using DOM stability detection via `waitTillHTMLStable` algorithm
     * Executes requested actions (navigation, clicking, typing, scrolling) with proper timing
     * Monitors network activity after clicks and waits for navigation when necessary
  3. **Visual Feedback** :
     * Captures optimized screenshots using WebP format (with PNG fallback)
     * Records browser console logs for debugging purposes
     * Tracks mouse position and maintains paginated history of actions
  4. **Session Management** :
     * Maintains browser state across multiple actions
     * Handles errors and automatically cleans up resources
     * Enforces proper workflow sequence (launch → interactions → close)


## Workflow Sequence[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#workflow-sequence "Direct link to Workflow Sequence")
Browser interactions must follow this specific sequence:
  1. **Session Initialization** : All browser workflows must start with a `launch` action
  2. **Interaction Phase** : Multiple `click`, `type`, and scroll actions can be performed
  3. **Session Termination** : All browser workflows must end with a `close` action
  4. **Tool Switching** : After closing the browser, other tools can be used


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#examples-when-used "Direct link to Examples When Used")
  * When creating a web form submission process, Roo launches a browser, navigates to the form, fills out fields with the `type` action, and clicks submit.
  * When testing a responsive website, Roo navigates to the site and uses scroll actions to examine different sections.
  * When capturing screenshots of a web application, Roo navigates through different pages and takes screenshots at each step.
  * When demonstrating an e-commerce checkout flow, Roo simulates the entire process from product selection to payment confirmation.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/browser-action#usage-examples "Direct link to Usage Examples")
Launching a browser and navigating to a website:
```
<browser_action><action>launch</action><url>https://example.com</url></browser_action>
```

Clicking at specific coordinates (e.g., a button):
```
<browser_action><action>click</action><coordinate>450,300</coordinate></browser_action>
```

Typing text into a focused input field:
```
<browser_action><action>type</action><text>Hello, World!</text></browser_action>
```

Scrolling down to see more content:
```
<browser_action><action>scroll_down</action></browser_action>
```

Closing the browser session:
```
<browser_action><action>close</action></browser_action>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/browser-action.md)
Last updated on **May 27, 2025**
[ Previousattempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)[Nextcodebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/browser-action#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/browser-action#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/browser-action#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/browser-action#key-features)
  * [Browser Modes](https://docs.roocode.com/advanced-usage/available-tools/browser-action#browser-modes)
    * [Local Browser Mode (Default)](https://docs.roocode.com/advanced-usage/available-tools/browser-action#local-browser-mode-default)
    * [Remote Browser Mode](https://docs.roocode.com/advanced-usage/available-tools/browser-action#remote-browser-mode)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/browser-action#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/browser-action#how-it-works)
  * [Workflow Sequence](https://docs.roocode.com/advanced-usage/available-tools/browser-action#workflow-sequence)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/browser-action#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/browser-action#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fbrowser-action)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



