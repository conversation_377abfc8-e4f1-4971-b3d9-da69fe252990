# execute_command Tool Documentation

*Tool: execute_command*
*Source: https://docs.roocode.com/advanced-usage/available-tools/execute-command*
*Scraped: 2025-06-03 14:27:52*
*Content length: 14000 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/execute-command#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/execute-command)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * execute_command


On this page
# execute_command
The `execute_command` tool runs CLI commands on the user's system. It allows Roo to perform system operations, install dependencies, build projects, start servers, and execute other terminal-based tasks needed to accomplish user objectives.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `command` (required): The CLI command to execute. Must be valid for the user's operating system.
  * `cwd` (optional): The working directory to execute the command in. If not provided, the current working directory is used.


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#what-it-does "Direct link to What It Does")
This tool executes terminal commands directly on the user's system, enabling a wide range of operations from file manipulations to running development servers. Commands run in managed terminal instances with real-time output capture, integrated with VS Code's terminal system for optimal performance and security.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#when-is-it-used "Direct link to When is it used?")
  * When installing project dependencies (npm install, pip install, etc.)
  * When building or compiling code (make, npm run build, etc.)
  * When starting development servers or running applications
  * When initializing new projects (git init, npm init, etc.)
  * When performing file operations beyond what other tools provide
  * When running tests or linting operations
  * When needing to execute specialized commands for specific technologies


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#key-features "Direct link to Key Features")
  * Integrates with VS Code shell API for reliable terminal execution
  * Reuses terminal instances when possible through a registry system
  * Captures command output line by line with real-time feedback
  * Supports long-running commands that continue in the background
  * Allows specification of custom working directories
  * Maintains terminal history and state across command executions
  * Handles complex command chains appropriate for the user's shell
  * Provides detailed command completion status and exit code interpretation
  * Supports interactive terminal applications with user feedback loop
  * Shows terminals during execution for transparency
  * Validates commands for security using shell-quote parsing
  * Blocks potentially dangerous subshell execution patterns
  * Integrates with RooIgnore system for file access control
  * Handles terminal escape sequences for clean output


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#limitations "Direct link to Limitations")
  * Command access may be restricted by RooIgnore rules and security validations
  * Commands with elevated permission requirements may need user configuration
  * Behavior may vary across operating systems for certain commands
  * Very long-running commands may require specific handling
  * File paths should be properly escaped according to the OS shell rules
  * Not all terminal features may work with remote development scenarios


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#how-it-works "Direct link to How It Works")
When the `execute_command` tool is invoked, it follows this process:
  1. **Command Validation and Security Checks** :
     * Parses the command using shell-quote to identify components
     * Validates against security restrictions (subshell usage, restricted files)
     * Checks against RooIgnore rules for file access permissions
     * Ensures the command meets system security requirements
  2. **Terminal Management** :
     * Gets or creates a terminal through TerminalRegistry
     * Sets up the working directory context
     * Prepares event listeners for output capture
     * Shows the terminal for user visibility
  3. **Command Execution and Monitoring** :
     * Executes via VS Code's shellIntegration API
     * Captures output with escape sequence processing
     * Throttles output handling (100ms intervals)
     * Monitors for command completion or errors
     * Detects "hot" processes like compilers for special handling
  4. **Result Processing** :
     * Strips ANSI/VS Code escape sequences for clean output
     * Interprets exit codes with detailed signal information
     * Updates working directory tracking if changed by command
     * Provides command status with appropriate context


## Terminal Implementation Details[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#terminal-implementation-details "Direct link to Terminal Implementation Details")
The tool uses a sophisticated terminal management system:
  1. **First Priority: Terminal Reuse**
     * The TerminalRegistry tries to reuse existing terminals when possible
     * This reduces proliferation of terminal instances and improves performance
     * Terminal state (working directory, history) is preserved across commands
  2. **Second Priority: Security Validation**
     * Commands are parsed using shell-quote for component analysis
     * Dangerous patterns like `$(...)` and backticks are blocked
     * Commands are checked against RooIgnore rules for file access control
     * A prefix-based allowlist system validates command patterns
  3. **Performance Optimizations**
     * Output is processed in 100ms throttled intervals to prevent UI overload
     * Zero-copy buffer management uses index-based tracking for efficiency
     * Special handling for compilation and "hot" processes
     * Platform-specific optimizations for Windows PowerShell
  4. **Error and Signal Handling**
     * Exit codes are mapped to detailed signal information (SIGTERM, SIGKILL, etc.)
     * Core dump detection for critical failures
     * Working directory changes are tracked and handled automatically
     * Clean recovery from terminal disconnection scenarios


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#examples-when-used "Direct link to Examples When Used")
  * When setting up a new project, Roo runs initialization commands like `npm init -y` followed by installing dependencies.
  * When building a web application, Roo executes build commands like `npm run build` to compile assets.
  * When deploying code, Roo runs git commands to commit and push changes to a repository.
  * When troubleshooting, Roo executes diagnostic commands to gather system information.
  * When starting a development server, Roo launches the appropriate server command (e.g., `npm start`).
  * When running tests, Roo executes the test runner command for the project's testing framework.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/execute-command#usage-examples "Direct link to Usage Examples")
Running a simple command in the current directory:
```
<execute_command><command>npm run dev</command></execute_command>
```

Installing dependencies for a project:
```
<execute_command><command>npm install express mongodb mongoose dotenv</command></execute_command>
```

Running multiple commands in sequence:
```
<execute_command><command>mkdir -p src/components && touch src/components/App.js</command></execute_command>
```

Executing a command in a specific directory:
```
<execute_command><command>git status</command><cwd>./my-project</cwd></execute_command>
```

Building and then starting a project:
```
<execute_command><command>npm run build && npm start</command></execute_command>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/execute-command.md)
Last updated on **May 27, 2025**
[ Previouscodebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)[Nextinsert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/execute-command#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/execute-command#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/execute-command#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/execute-command#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/execute-command#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/execute-command#how-it-works)
  * [Terminal Implementation Details](https://docs.roocode.com/advanced-usage/available-tools/execute-command#terminal-implementation-details)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/execute-command#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/execute-command#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fexecute-command)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



