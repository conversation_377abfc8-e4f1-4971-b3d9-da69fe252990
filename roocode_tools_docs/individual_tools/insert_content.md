# insert_content Tool Documentation

*Tool: insert_content*
*Source: https://docs.roocode.com/advanced-usage/available-tools/insert-content*
*Scraped: 2025-06-03 14:29:52*
*Content length: 11218 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/insert-content#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/insert-content)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * insert_content


On this page
# insert_content
The `insert_content` tool adds new lines of content into an existing file without modifying the original content. It's ideal for inserting code blocks, configuration entries, or log lines at specific locations.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The relative path (from the workspace root) of the file to insert content into.
  * `line` (required): The 1-based line number _before_ which the content should be inserted. Use `0` to append the content to the end of the file.
  * `content` (required): The text content to insert.


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#what-it-does "Direct link to What It Does")
This tool reads the target file, identifies the specified insertion point based on the `line` parameter, and inserts the provided `content` at that location. If `line` is `0`, the content is added to the end. Changes are presented in a diff view for user approval before being saved.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#when-is-it-used "Direct link to When is it used?")
  * When adding new import statements at the beginning of a file.
  * When inserting new functions or methods into existing code.
  * When adding configuration blocks to settings files.
  * When appending log entries or data records.
  * When adding any multi-line text block without altering existing lines.


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#key-features "Direct link to Key Features")
  * **Targeted Insertion** : Adds content precisely at the specified line number or appends to the end.
  * **Preserves Existing Content** : Does not modify or delete original file lines.
  * **Interactive Approval** : Shows proposed insertions in a diff view, requiring explicit user approval.
  * **User Edit Support** : Allows editing the proposed content directly within the diff view before final approval.
  * **Handles Line Numbers** : Correctly interprets the `line` parameter (1-based or 0 for append).
  * **Context Tracking** : Records the file edit operation for context management.
  * **Error Handling** : Checks for missing parameters, invalid line numbers, and file access issues.


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#limitations "Direct link to Limitations")
  * **Insert Only** : Cannot replace or delete existing content. Use `apply_diff` or `search_and_replace` for modifications.
  * **Requires Existing File** : The target file specified by `path` must exist.
  * **Review Overhead** : The mandatory diff view approval adds an interactive step.


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#how-it-works "Direct link to How It Works")
When the `insert_content` tool is invoked, it follows this process:
  1. **Parameter Validation** : Checks for required `path`, `line`, and `content`. Validates `line` is a non-negative integer.
  2. **File Reading** : Reads the content of the target file specified by `path`.
  3. **Insertion Point Calculation** : Converts the 1-based `line` parameter to a 0-based index for internal processing (`-1` for appending).
  4. **Content Insertion** : Uses an internal utility (`insertGroups`) to merge the original file lines with the new `content` at the calculated index.
  5. **Diff View Interaction** : 
     * Opens the file in the diff view (`cline.diffViewProvider.open`).
     * Updates the diff view with the proposed content (`cline.diffViewProvider.update`).
  6. **User Approval** : Presents the change via `askApproval`. Reverts if rejected.
  7. **Saving Changes** : If approved, saves the changes using `cline.diffViewProvider.saveChanges`.
  8. **File Context Tracking** : Tracks the edit using `cline.getFileContextTracker().trackFileContext`.
  9. **Handling User Edits** : If the user edited the content in the diff view, reports the final merged content back.
  10. **Result Reporting** : Reports success (including user edits) or failure back to the AI model.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/insert-content#usage-examples "Direct link to Usage Examples")
Inserting import statements at the beginning of a file (`line: 1`):
```
<insert_content><path>src/utils.ts</path><line>1</line><content>// Add imports at start of fileimport { sum } from './math';import { parse } from 'date-fns';</content></insert_content>
```

Appending content to the end of a file (`line: 0`):
```
<insert_content><path>config/routes.yaml</path><line>0</line><content>- path: /new-feature component: NewFeatureComponent auth_required: true</content></insert_content>
```

Inserting a function before line 50:
```
<insert_content><path>src/services/api.js</path><line>50</line><content>async function fetchUserData(userId) { const response = await fetch(`/api/users/${userId}`); if (!response.ok) {  throw new Error('Failed to fetch user data'); } return response.json();}</content></insert_content>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/insert-content.md)
Last updated on **May 27, 2025**
[ Previousexecute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)[Nextlist_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/insert-content#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/insert-content#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/insert-content#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/insert-content#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/insert-content#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/insert-content#how-it-works)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/insert-content#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Finsert-content)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



