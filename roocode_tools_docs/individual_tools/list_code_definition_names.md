# list_code_definition_names Tool Documentation

*Tool: list_code_definition_names*
*Source: https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names*
*Scraped: 2025-06-03 14:29:34*
*Content length: 12986 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * list_code_definition_names


On this page
# list_code_definition_names
The `list_code_definition_names` tool provides a structural overview of your codebase by listing code definitions from source files at the top level of a specified directory. It helps Roo understand code architecture by displaying line numbers and definition snippets.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The path of the directory to list top level source code definitions for, relative to the current working directory


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#what-it-does "Direct link to What It Does")
This tool scans source code files at the top level of a specified directory and extracts code definitions like classes, functions, and interfaces. It displays the line numbers and actual code for each definition, providing a quick way to map the important components of your codebase.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to understand your codebase architecture quickly
  * When Roo needs to locate important code constructs across multiple files
  * When planning refactoring or extensions to existing code
  * Before diving into implementation details with other tools
  * When identifying relationships between different parts of your codebase


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#key-features "Direct link to Key Features")
  * Extracts classes, functions, methods, interfaces, and other definitions from source files
  * Displays line numbers and actual source code for each definition
  * Supports multiple programming languages including JavaScript, TypeScript, Python, Rust, Go, C++, C, C#, Ruby, Java, PHP, Swift, and Kotlin
  * Processes only files at the top level of the specified directory (not subdirectories)
  * Limits processing to a maximum of 50 files for performance
  * Focuses on top-level definitions to avoid overwhelming detail
  * Helps identify code organization patterns across the project
  * Creates a mental map of your codebase's architecture
  * Works in conjunction with other tools like `read_file` for deeper analysis


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#limitations "Direct link to Limitations")
  * Only identifies top-level definitions, not nested ones
  * Only processes files at the top level of the specified directory, not subdirectories
  * Limited to processing a maximum of 50 files per request
  * Dependent on language-specific parsers, with varying detection quality
  * May not recognize all definitions in languages with complex syntax
  * Not a substitute for reading code to understand implementation details
  * Cannot detect runtime patterns or dynamic code relationships
  * Does not provide information about how definitions are used
  * May have reduced accuracy with highly dynamic or metaprogrammed code
  * Limited to specific languages supported by the implemented Tree-sitter parsers


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#how-it-works "Direct link to How It Works")
When the `list_code_definition_names` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates the required `path` parameter
  2. **Path Resolution** : Resolves the relative path to an absolute path
  3. **Directory Scanning** : Scans only the top level of the specified directory for source code files (not recursive)
  4. **File Filtering** : Limits processing to a maximum of 50 files
  5. **Language Detection** : Identifies file types based on extensions (.js, .jsx, .ts, .tsx, .py, .rs, .go, .cpp, .hpp, .c, .h, .cs, .rb, .java, .php, .swift, .kt, .kts)
  6. **Code Parsing** : Uses Tree-sitter to parse code and extract definitions through these steps: 
     * Parsing file content into an Abstract Syntax Tree (AST)
     * Creating a query using a language-specific query string
     * Sorting the captures by their position in the file
  7. **Result Formatting** : Outputs definitions with line numbers and actual source code


## Output Format[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#output-format "Direct link to Output Format")
The output shows file paths followed by line numbers and the actual source code of each definition. For example:
```
src/utils.js:0--0 | export class HttpClient {5--5 | formatDate() {10--10 | function parseConfig(data) {src/models/User.js:0--0 | interface UserProfile {10--10 | export class User {20--20 | function createUser(data) {
```

Each line displays:
  * The start and end line numbers of the definition
  * The pipe symbol (|) as a separator
  * The actual source code of the definition


This output format helps you quickly see both where definitions are located in the file and their implementation details.
## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#examples-when-used "Direct link to Examples When Used")
  * When starting a new task, Roo first lists key code definitions to understand the overall structure of your project.
  * When planning refactoring work, Roo uses this tool to identify classes and functions that might be affected.
  * When exploring unfamiliar codebases, Roo maps the important code constructs before diving into implementation details.
  * When adding new features, Roo identifies existing patterns and relevant code definitions to maintain consistency.
  * When troubleshooting bugs, Roo maps the codebase structure to locate potential sources of the issue.
  * When planning architecture changes, Roo identifies all affected components across files.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#usage-examples "Direct link to Usage Examples")
Listing code definitions in the current directory:
```
<list_code_definition_names><path>.</path></list_code_definition_names>
```

Examining a specific module's structure:
```
<list_code_definition_names><path>src/components</path></list_code_definition_names>
```

Exploring a utility library:
```
<list_code_definition_names><path>lib/utils</path></list_code_definition_names>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/list-code-definition-names.md)
Last updated on **May 27, 2025**
[ Previousinsert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)[Nextlist_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#how-it-works)
  * [Output Format](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#output-format)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Flist-code-definition-names)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



