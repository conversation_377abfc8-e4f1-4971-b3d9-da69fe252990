# list_files Tool Documentation

*Tool: list_files*
*Source: https://docs.roocode.com/advanced-usage/available-tools/list-files*
*Scraped: 2025-06-03 14:27:41*
*Content length: 12588 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/list-files#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/list-files)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/list-files)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/list-files)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/list-files)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/list-files)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/list-files)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/list-files)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * list_files


On this page
# list_files
The `list_files` tool displays the files and directories within a specified location. It helps Roo understand your project structure and navigate your codebase effectively.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The path of the directory to list contents for, relative to the current working directory
  * `recursive` (optional): Whether to list files recursively. Use `true` for recursive listing, `false` or omit for top-level only.


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#what-it-does "Direct link to What It Does")
This tool lists all files and directories in a specified location, providing a clear overview of your project structure. It can either show just the top-level contents or recursively explore subdirectories.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to understand your project structure
  * When Roo explores what files are available before reading specific ones
  * When Roo maps a codebase to better understand its organization
  * Before using more targeted tools like `read_file` or `search_files`
  * When Roo needs to check for specific file types (like configuration files) across a project


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#key-features "Direct link to Key Features")
  * Lists both files and directories with directories clearly marked
  * Offers both recursive and non-recursive listing modes
  * Intelligently ignores common large directories like `node_modules` and `.git` in recursive mode
  * Respects `.gitignore` rules when in recursive mode
  * Marks files ignored by `.rooignore` with a lock symbol (🔒) when `showRooIgnoredFiles` is enabled
  * Optimizes file listing performance by leveraging the `ripgrep` tool.
  * Sorts results to show directories before their contents, maintaining a logical hierarchy
  * Presents results in a clean, organized format
  * Automatically creates a mental map of your project structure


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#limitations "Direct link to Limitations")
  * File listing is capped at about 200 files by default to prevent performance issues
  * The underlying `ripgrep` file listing process has a 10-second timeout; if exceeded, partial results may be returned.
  * When the file limit is hit, it adds a note suggesting to use `list_files` on specific subdirectories
  * Not designed for confirming the existence of files you've just created
  * May have reduced performance in very large directory structures
  * Cannot list files in root or home directories for security reasons


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#how-it-works "Direct link to How It Works")
When the `list_files` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates the required `path` parameter and optional `recursive` parameter
  2. **Path Resolution** : Resolves the relative path to an absolute path
  3. **Security Checks** : Prevents listing files in sensitive locations like root or home directories
  4. **Directory/File Scanning** : 
     * Uses the `ripgrep` tool to efficiently list files, applying a 10-second timeout.
     * Uses Node.js `fs` module to list directories.
     * Applies different filtering logic for recursive vs. non-recursive modes.
  5. **Result Filtering** : 
     * In recursive mode, skips common large directories like `node_modules`, `.git`, etc.
     * Respects `.gitignore` rules when in recursive mode
     * Handles `.rooignore` patterns, either hiding files or marking them with a lock symbol
  6. **Formatting** : 
     * Marks directories with a trailing slash (`/`)
     * Sorts results to show directories before their contents for logical hierarchy
     * Marks ignored files with a lock symbol (🔒) when `showRooIgnoredFiles` is enabled
     * Caps results at 200 files by default with a note about using subdirectories
     * Organizes results for readability


## File Listing Format[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#file-listing-format "Direct link to File Listing Format")
The file listing results include:
  * Each file path is displayed on its own line
  * Directories are marked with a trailing slash (`/`)
  * Files ignored by `.rooignore` are marked with a lock symbol (🔒) when `showRooIgnoredFiles` is enabled
  * Results are sorted logically with directories appearing before their contents
  * When the file limit is reached, a message appears suggesting to use `list_files` on specific subdirectories


Example output format:
```
src/src/components/src/components/Button.tsxsrc/components/Header.tsxsrc/utils/src/utils/helpers.tssrc/index.ts...File listing truncated (showing 200 of 543 files). Use list_files on specific subdirectories for more details.
```

When `.rooignore` files are used and `showRooIgnoredFiles` is enabled:
```
src/src/components/src/components/Button.tsxsrc/components/Header.tsx🔒 src/secrets.jsonsrc/utils/src/utils/helpers.tssrc/index.ts
```

## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#examples-when-used "Direct link to Examples When Used")
  * When starting a new task, Roo may list the project files to understand its structure before diving into specific code.
  * When asked to find specific types of files (like all JavaScript files), Roo first lists directories to know where to look.
  * When providing recommendations for code organization, Roo examines the current project structure first.
  * When setting up a new feature, Roo lists related directories to understand the project conventions.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/list-files#usage-examples "Direct link to Usage Examples")
Listing top-level files in the current directory:
```
<list_files><path>.</path></list_files>
```

Recursively listing all files in a source directory:
```
<list_files><path>src</path><recursive>true</recursive></list_files>
```

Examining a specific project subdirectory:
```
<list_files><path>src/components</path><recursive>false</recursive></list_files>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/list-files.md)
Last updated on **May 27, 2025**
[ Previouslist_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)[Nextnew_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/list-files#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/list-files#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/list-files#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/list-files#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/list-files#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/list-files#how-it-works)
  * [File Listing Format](https://docs.roocode.com/advanced-usage/available-tools/list-files#file-listing-format)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/list-files#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/list-files#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Flist-files)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



