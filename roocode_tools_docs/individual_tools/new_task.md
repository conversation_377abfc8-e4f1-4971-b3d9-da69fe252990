# new_task Tool Documentation

*Tool: new_task*
*Source: https://docs.roocode.com/advanced-usage/available-tools/new-task*
*Scraped: 2025-06-03 16:43:19*
*Content length: 11475 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/new-task#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/new-task)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/new-task)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/new-task)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/new-task)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/new-task)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/new-task)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/new-task)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * new_task


On this page
# new_task
The `new_task` tool creates subtasks with specialized modes while maintaining a parent-child relationship. It breaks down complex projects into manageable pieces, each operating in the mode best suited for specific work.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `mode` (required): The slug of the mode to start the new task in (e.g., "code", "ask", "architect")
  * `message` (required): The initial user message or instructions for this new task


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#what-it-does "Direct link to What It Does")
This tool creates a new task instance with a specified starting mode and initial message. It allows complex workflows to be divided into subtasks with their own conversation history. Parent tasks are paused during subtask execution and resumed when the subtask completes, with results transferred back to the parent.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#when-is-it-used "Direct link to When is it used?")
  * When breaking down complex projects into separate, focused subtasks
  * When different aspects of a task require different specialized modes
  * When different phases of work benefit from context separation
  * When organizing multi-phase development workflows


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#key-features "Direct link to Key Features")
  * Creates subtasks with their own conversation history and specialized mode
  * Pauses parent tasks for later resumption
  * Maintains hierarchical task relationships for navigation
  * Transfers results back to parent tasks upon completion
  * Supports workflow segregation for complex projects
  * Allows different parts of a project to use modes optimized for specific work
  * Requires explicit user approval for task creation
  * Provides clear task transition in the UI


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#limitations "Direct link to Limitations")
  * Cannot create tasks with modes that don't exist
  * Requires user approval before creating each new task
  * Task interface may become complex with deeply nested subtasks
  * Subtasks inherit certain workspace and extension configurations from parents
  * May require re-establishing context when switching between deeply nested tasks
  * Task completion needs explicit signaling to properly return to parent tasks


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#how-it-works "Direct link to How It Works")
When the `new_task` tool is invoked, it follows this process:
  1. **Parameter Validation** :
     * Validates the required `mode` and `message` parameters
     * Verifies that the requested mode exists in the system
  2. **Task Stack Management** :
     * Maintains a task stack that tracks all active and paused tasks
     * Preserves the current mode for later resumption
     * Sets the parent task to paused state
  3. **Task Context Management** :
     * Creates a new task context with the provided message
     * Assigns unique taskId and instanceId identifiers for state management
     * Captures telemetry data on tool usage and task lifecycles
  4. **Mode Switching and Integration** :
     * Switches to the specified mode with appropriate role and capabilities
     * Initializes the new task with the provided message
     * Integrates with VS Code's command palette and code actions
  5. **Task Completion and Result Transfer** :
     * When subtask completes, result is passed back to parent task via `finishSubTask()`
     * Parent task resumes in its original mode
     * Task history and token usage metrics are updated
     * The `taskCompleted` event is emitted with performance data


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#examples-when-used "Direct link to Examples When Used")
  * When a front-end developer needs to architect a new feature, implement the code, and document it, they can create separate tasks for each phase with results flowing from one phase to the next.
  * When debugging an issue before implementing a fix, the debugging task can document findings that are passed to the implementation task.
  * When developing a full-stack application, database schema designs from an architect-mode task inform implementation details in a subsequent code-mode task.
  * When documenting a system after implementation, the documentation task can reference the completed implementation while using documentation-specific features.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/new-task#usage-examples "Direct link to Usage Examples")
Creating a new task in code mode:
```
<new_task><mode>code</mode><message>Implement a user authentication service with login, registration, and password reset functionality.</message></new_task>
```

Creating a documentation task after completing implementation:
```
<new_task><mode>docs</mode><message>Create comprehensive API documentation for the authentication service we just built.</message></new_task>
```

Breaking down a complex feature into architectural planning and implementation:
```
<new_task><mode>architect</mode><message>Design the database schema and system architecture for our new e-commerce platform.</message></new_task>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/new-task.md)
Last updated on **May 27, 2025**
[ Previouslist_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)[Nextread_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/new-task#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/new-task#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/new-task#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/new-task#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/new-task#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/new-task#how-it-works)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/new-task#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/new-task#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fnew-task)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



