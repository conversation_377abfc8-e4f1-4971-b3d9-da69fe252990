# read_file Tool Documentation

*Source: https://docs.roocode.com/advanced-usage/available-tools/read-file*
*Scraped: 2025-06-03 10:52:02*
*Content length: 15069 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/read-file#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/read-file)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/read-file)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/read-file)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/read-file)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/read-file)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/read-file)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/read-file)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * read_file


On this page
# read_file
The `read_file` tool examines the contents of files in a project. It allows Roo to understand code, configuration files, and documentation to provide better assistance.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The path of the file to read relative to the current working directory
  * `start_line` (optional): The starting line number to read from (1-based indexing)
  * `end_line` (optional): The ending line number to read to (1-based, inclusive)


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#what-it-does "Direct link to What It Does")
This tool reads the content of a specified file and returns it with line numbers for easy reference. It can read entire files or specific sections, and even extract text from PDFs and Word documents.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to understand existing code structure
  * When Roo needs to analyze configuration files
  * When Roo needs to extract information from text files
  * When Roo needs to see code before suggesting changes
  * When specific line numbers need to be referenced in discussions


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#key-features "Direct link to Key Features")
  * Displays file content with line numbers for easy reference
  * Can read specific portions of files by specifying line ranges
  * Extracts readable text from PDF and DOCX files
  * Automatically truncates large text files when no line range is specified, showing the beginning of the file
  * Provides method summaries with line ranges for truncated large code files
  * Efficiently streams only requested line ranges for better performance
  * Makes it easy to discuss specific parts of code with line numbering


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#limitations "Direct link to Limitations")
  * May not handle extremely large files efficiently without using line range parameters
  * For binary files (except PDF and DOCX), may return content that isn't human-readable


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#how-it-works "Direct link to How It Works")
When the `read_file` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates the required `path` parameter and optional parameters
  2. **Path Resolution** : Resolves the relative path to an absolute path
  3. **Reading Strategy Selection** : 
     * The tool uses a strict priority hierarchy (explained in detail below)
     * It chooses between range reading, auto-truncation, or full file reading
  4. **Content Processing** : 
     * Adds line numbers to the content (e.g., "1 | const x = 13") where `1 |` is the line number.
     * For truncated files, adds truncation notice and method definitions
     * For special formats (PDF, DOCX), extracts readable text


## Reading Strategy Priority[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-strategy-priority "Direct link to Reading Strategy Priority")
The tool uses a clear decision hierarchy to determine how to read a file:
  1. **First Priority: Explicit Line Range**
     * If either `start_line` or `end_line` is provided, the tool always performs a range read
     * The implementation efficiently streams only the requested lines, making it suitable for processing large files
     * This takes precedence over all other options
  2. **Second Priority: Automatic Truncation for Large Text Files**
     * This applies only when **all** of the following conditions are met: 
       * Neither `start_line` nor `end_line` is specified.
       * The file is identified as a text-based file (not binary like PDF/DOCX).
       * The file's total line count exceeds an internal limit (e.g., `maxReadFileLine`, often around 500 lines).
     * When automatic truncation occurs: 
       * The tool reads only the _first_ `maxReadFileLine` lines.
       * It appends a notice indicating truncation (e.g., `[Showing only 500 of 1200 total lines...]`).
       * For code files, it may also append a summary of source code definitions found within the truncated portion.
  3. **Default Behavior: Read Entire File**
     * If neither an explicit range is given nor automatic truncation applies (e.g., the file is within the line limit, or it's a supported binary type), the tool reads the entire content.
     * For supported formats like PDF and DOCX, it attempts to extract the full text content.


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#examples-when-used "Direct link to Examples When Used")
  * When asked to explain or improve code, Roo first reads the relevant files to understand the current implementation.
  * When troubleshooting configuration issues, Roo reads config files to identify potential problems.
  * When working with documentation, Roo reads existing docs to understand the current content before suggesting improvements.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#usage-examples "Direct link to Usage Examples")
Here are several scenarios demonstrating how the `read_file` tool is used and the typical output you might receive.
### Reading an Entire File[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-an-entire-file "Direct link to Reading an Entire File")
To read the complete content of a file:
**Input:**
```
<read_file><path>src/app.js</path></read_file>
```

**Simulated Output (for a small file like`example_small.txt`):**
```
1 | This is the first line.2 | This is the second line.3 | This is the third line.
```

_(Output will vary based on the actual file content)_
### Reading Specific Lines[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-specific-lines "Direct link to Reading Specific Lines")
To read only a specific range of lines (e.g., 46-68):
**Input:**
```
<read_file><path>src/app.js</path><start_line>46</start_line><end_line>68</end_line></read_file>
```

**Simulated Output (for lines 2-3 of`example_five_lines.txt`):**
```
2 | Content of line two.3 | Content of line three.
```

_(Output shows only the requested lines with their original line numbers)_
### Reading a Large Text File (Automatic Truncation)[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-a-large-text-file-automatic-truncation "Direct link to Reading a Large Text File \(Automatic Truncation\)")
When reading a large text file without specifying a line range, the tool automatically truncates the content if it exceeds the internal line limit (e.g., 500 lines).
**Input:**
```
<read_file><path>logs/large_app.log</path></read_file>
```

**Simulated Output (for a 1500-line log file with a 500-line limit):**
```
1 | Log entry 1...2 | Log entry 2......500 | Log entry 500...[Showing only 500 of 1500 total lines. Use start_line and end_line to read specific ranges.]// Optional: Source code definitions summary might appear here for code files
```

_(Output shows the beginning lines up to the internal limit, plus a truncation notice. Use line ranges for full access.)_
### Attempting to Read a Non-Existent File[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#attempting-to-read-a-non-existent-file "Direct link to Attempting to Read a Non-Existent File")
If the specified file does not exist:
**Input:**
```
<read_file><path>non_existent_file.txt</path></read_file>
```

**Simulated Output (Error):**
```
Error: File not found at path 'non_existent_file.txt'.
```

### Attempting to Read a Blocked File[​](https://docs.roocode.com/advanced-usage/available-tools/read-file#attempting-to-read-a-blocked-file "Direct link to Attempting to Read a Blocked File")
If the file is excluded by rules in a `.rooignore` file:
**Input:**
```
<read_file><path>.env</path></read_file>
```

**Simulated Output (Error):**
```
Error: Access denied to file '.env' due to .rooignore rules.
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/read-file.md)
Last updated on **May 27, 2025**
[ Previousnew_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)[Nextsearch_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/read-file#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/read-file#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/read-file#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/read-file#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/read-file#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/read-file#how-it-works)
  * [Reading Strategy Priority](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-strategy-priority)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/read-file#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/read-file#usage-examples)
    * [Reading an Entire File](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-an-entire-file)
    * [Reading Specific Lines](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-specific-lines)
    * [Reading a Large Text File (Automatic Truncation)](https://docs.roocode.com/advanced-usage/available-tools/read-file#reading-a-large-text-file-automatic-truncation)
    * [Attempting to Read a Non-Existent File](https://docs.roocode.com/advanced-usage/available-tools/read-file#attempting-to-read-a-non-existent-file)
    * [Attempting to Read a Blocked File](https://docs.roocode.com/advanced-usage/available-tools/read-file#attempting-to-read-a-blocked-file)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fread-file)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



