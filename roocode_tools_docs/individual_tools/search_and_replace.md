# search_and_replace Tool Documentation

*Tool: search_and_replace*
*Source: https://docs.roocode.com/advanced-usage/available-tools/search-and-replace*
*Scraped: 2025-06-03 14:29:57*
*Content length: 12179 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * search_and_replace


On this page
# search_and_replace
The `search_and_replace` tool finds and replaces text within a file, supporting both literal strings and regular expression patterns. It allows for targeted replacements across multiple locations, optionally within specific line ranges.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#parameters "Direct link to Parameters")
### Required Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#required-parameters "Direct link to Required Parameters")
  * `path`: The relative path (from the workspace root) of the file to modify.
  * `search`: The text string or regex pattern to find.
  * `replace`: The text to replace matches with.


### Optional Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#optional-parameters "Direct link to Optional Parameters")
  * `start_line`: The 1-based line number where the search scope begins.
  * `end_line`: The 1-based line number where the search scope ends (inclusive).
  * `use_regex`: Set to `"true"` to treat the `search` parameter as a regular expression pattern (default is `false`).
  * `ignore_case`: Set to `"true"` to perform a case-insensitive search (default is `false`).


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#what-it-does "Direct link to What It Does")
This tool reads the specified file and performs a search-and-replace operation based on the provided parameters. It can operate on the entire file or be restricted to a specific range of lines. Changes are presented in a diff view for user review and approval before being saved.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#when-is-it-used "Direct link to When is it used?")
  * When renaming variables, functions, or classes across a file.
  * When updating specific text strings or values consistently.
  * When applying patterned changes using regular expressions.
  * When refactoring code requires replacing specific patterns.
  * When making targeted changes within a defined section of a file.


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#key-features "Direct link to Key Features")
  * **Flexible Searching** : Supports both literal text and regular expression patterns.
  * **Case Sensitivity Control** : Option to ignore case during search.
  * **Scoped Replacements** : Can limit replacements to a specific range of lines (`start_line`, `end_line`).
  * **Global Replacement** : Performs replacements across the entire file (or specified range) by default.
  * **Interactive Approval** : Shows proposed changes in a diff view for user review and approval.
  * **User Edit Support** : Allows editing the proposed content directly within the diff view.
  * **Context Tracking** : Records the file edit operation.
  * **Error Handling** : Checks for missing parameters, file access issues, and invalid line numbers.


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#limitations "Direct link to Limitations")
  * **Single File Operation** : Operates on only one file at a time. Use `search_files` to find patterns across multiple files first.
  * **Review Overhead** : The mandatory diff view approval adds an interactive step.
  * **Regex Complexity** : Complex regex patterns might require careful construction and testing.


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#how-it-works "Direct link to How It Works")
When the `search_and_replace` tool is invoked, it follows this process:
  1. **Parameter Validation** : Checks for required `path`, `search`, `replace`, and validates optional parameters like line numbers and boolean flags.
  2. **File Reading** : Reads the content of the target file specified by `path`.
  3. **Regex Construction** : 
     * If `use_regex` is `false`, the `search` string is escaped to treat it as literal text.
     * A `RegExp` object is created with the `g` (global) flag and optionally the `i` (ignore case) flag.
  4. **Replacement Execution** : 
     * If `start_line` or `end_line` are provided, the file content is split into lines, the relevant section is isolated, the replacement is performed on that section, and the file content is reconstructed.
     * If no line range is specified, the replacement is performed on the entire file content string.
  5. **Diff View Interaction** : 
     * Opens the file in the diff view showing original vs. proposed content.
     * Updates the diff view with the result of the replacement.
  6. **User Approval** : Presents the change via `askApproval`. Reverts if rejected.
  7. **Saving Changes** : If approved, saves the changes (including any user edits made in the diff view).
  8. **File Context Tracking** : Tracks the edit using `cline.getFileContextTracker().trackFileContext`.
  9. **Result Reporting** : Reports success (including user edits) or failure back to the AI model.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#usage-examples "Direct link to Usage Examples")
Simple text replacement throughout a file:
```
<search_and_replace><path>src/config.js</path><search>API_KEY_OLD</search><replace>API_KEY_NEW</replace></search_and_replace>
```

Case-insensitive regex replacement to update function calls:
```
<search_and_replace><path>src/app.ts</path><search>processData\((.*?)\)</search><replace>handleData($1)</replace><use_regex>true</use_regex><ignore_case>true</ignore_case></search_and_replace>
```

Replacing text only within lines 10 to 20:
```
<search_and_replace><path>README.md</path><search>Draft</search><replace>Final</replace><start_line>10</start_line><end_line>20</end_line></search_and_replace>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/search-and-replace.md)
Last updated on **May 27, 2025**
[ Previoussearch_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)[Nextswitch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#parameters)
    * [Required Parameters](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#required-parameters)
    * [Optional Parameters](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#optional-parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#how-it-works)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fsearch-and-replace)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



