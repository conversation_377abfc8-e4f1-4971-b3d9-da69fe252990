# search_files Tool Documentation

*Tool: search_files*
*Source: https://docs.roocode.com/advanced-usage/available-tools/search-files*
*Scraped: 2025-06-03 14:29:39*
*Content length: 12044 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/search-files#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/search-files)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/search-files)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/search-files)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/search-files)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/search-files)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/search-files)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/search-files)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * search_files


On this page
# search_files
The `search_files` tool performs regex searches across multiple files in your project. It helps Roo locate specific code patterns, text, or other content throughout your codebase with contextual results.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The path of the directory to search in, relative to the current working directory
  * `regex` (required): The regular expression pattern to search for (uses Rust regex syntax)
  * `file_pattern` (optional): Glob pattern to filter files (e.g., '*.ts' for TypeScript files)


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#what-it-does "Direct link to What It Does")
This tool searches across files in a specified directory using regular expressions, showing each match with surrounding context. It's like having a powerful "Find in Files" feature that works across the entire project structure.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to find where specific functions or variables are used
  * When Roo helps with refactoring and needs to understand usage patterns
  * When Roo needs to locate all instances of a particular code pattern
  * When Roo searches for text across multiple files with filtering capabilities


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#key-features "Direct link to Key Features")
  * Searches across multiple files in a single operation using high-performance Ripgrep
  * Shows context around each match (1 line before and after)
  * Filters files by type using glob patterns (e.g., only TypeScript files)
  * Provides line numbers for easy reference
  * Uses powerful regex patterns for precise searches
  * Automatically limits output to 300 results with notification
  * Truncates lines longer than 500 characters with "[truncated...]" marker
  * Intelligently combines nearby matches into single blocks for readability


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#limitations "Direct link to Limitations")
  * Works best with text-based files (not effective for binary files like images)
  * Performance may slow with extremely large codebases
  * Uses Rust regex syntax, which may differ slightly from other regex implementations
  * Cannot search within compressed files or archives
  * Default context size is fixed (1 line before and after)
  * May display varying context sizes when matches are close together due to result grouping


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#how-it-works "Direct link to How It Works")
When the `search_files` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates the required `path` and `regex` parameters
  2. **Path Resolution** : Resolves the relative path to an absolute path
  3. **Search Execution** : 
     * Uses Ripgrep (rg) for high-performance text searching
     * Applies file pattern filtering if specified
     * Collects matches with surrounding context
  4. **Result Formatting** : 
     * Formats results with file paths, line numbers, and context
     * Displays 1 line of context before and after each match
     * Structures output for easy readability
     * Limits results to a maximum of 300 matches with notification
     * Truncates lines longer than 500 characters
     * Merges nearby matches into contiguous blocks


## Search Results Format[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#search-results-format "Direct link to Search Results Format")
The search results include:
  * Relative file paths for each matching file (prefixed with #)
  * Context lines before and after each match (1 line by default)
  * Line numbers padded to 3 spaces followed by `|` and the line content
  * A separator line (----) after each match group


Example output format:
```
# rel/path/to/app.ts 11 |  // Some processing logic here 12 |  // TODO: Implement error handling 13 |  return processedData;----# Showing first 300 of 300+ results. Use a more specific search if necessary.
```

When matches occur close to each other, they're merged into a single block rather than shown as separate results:
```
# rel/path/to/auth.ts 13 | // Some code here 14 | // TODO: Add proper validation 15 | function validateUser(credentials) { 16 |  // TODO: Implement rate limiting 17 |  return checkDatabase(credentials);----
```

## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#examples-when-used "Direct link to Examples When Used")
  * When asked to refactor a function, Roo first searches for all places the function is used to ensure comprehensive changes.
  * When investigating bugs, Roo searches for similar patterns to identify related issues across the codebase.
  * When addressing technical debt, Roo locates all TODO comments across the project.
  * When analyzing dependencies, Roo finds all imports of a particular module.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/search-files#usage-examples "Direct link to Usage Examples")
Searching for TODO comments in all JavaScript files:
```
<search_files><path>src</path><regex>TODO|FIXME</regex><file_pattern>*.js</file_pattern></search_files>
```

Finding all usages of a specific function:
```
<search_files><path>.</path><regex>function\s+calculateTotal</regex><file_pattern>*.{js,ts}</file_pattern></search_files>
```

Searching for a specific import pattern across the entire project:
```
<search_files><path>.</path><regex>import\s+.*\s+from\s+['"]@components/</regex></search_files>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/search-files.md)
Last updated on **May 27, 2025**
[ Previousread_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)[Nextsearch_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/search-files#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/search-files#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/search-files#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/search-files#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/search-files#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/search-files#how-it-works)
  * [Search Results Format](https://docs.roocode.com/advanced-usage/available-tools/search-files#search-results-format)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/search-files#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/search-files#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fsearch-files)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



