# switch_mode Tool Documentation

*Tool: switch_mode*
*Source: https://docs.roocode.com/advanced-usage/available-tools/switch-mode*
*Scraped: 2025-06-03 16:43:15*
*Content length: 14390 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-dark.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/getting-started/installing)
  * [Features](https://docs.roocode.com/features/api-configuration-profiles)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/providers/anthropic)
  * [FAQ](https://docs.roocode.com/faq)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/community/)
  * [Update Notes](https://docs.roocode.com/update-notes/)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * switch_mode


On this page
# switch_mode
The `switch_mode` tool enables Roo to change between different operational modes, each with specialized capabilities for specific types of tasks. This allows seamless transitions between modes like Code, Architect, Ask, or Debug when the current task requires different expertise.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `mode_slug` (required): The slug of the mode to switch to (e.g., "code", "ask", "architect")
  * `reason` (optional): The reason for switching modes, providing context for the user


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#what-it-does "Direct link to What It Does")
This tool requests a mode change when the current task would be better handled by another mode's capabilities. It maintains context while shifting Roo's focus and available toolsets to match the requirements of the new task phase.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#when-is-it-used "Direct link to When is it used?")
  * When transitioning from information gathering to code implementation
  * When shifting from coding to architecture or design
  * When the current task requires capabilities only available in a different mode
  * When specialized expertise is needed for a particular phase of a complex project


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#key-features "Direct link to Key Features")
  * Maintains context continuity across mode transitions
  * Provides clear reasoning for mode switch recommendations
  * Requires user approval for all mode changes
  * Enforces tool group restrictions specific to each mode
  * Seamlessly adapts tool availability based on the selected mode
  * Works with both standard and custom modes
  * Displays the mode switch and reasoning in the UI
  * Uses XML-style formatting for parameter specification
  * Handles file type restrictions specific to certain modes


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#limitations "Direct link to Limitations")
  * Cannot switch to modes that don't exist in the system
  * Requires explicit user approval for each mode transition
  * Cannot use tools specific to a mode until the switch is complete
  * Applies a 500ms delay after mode switching to allow the change to take effect
  * Some modes have file type restrictions (e.g., Architect mode can only edit markdown files)
  * Mode preservation for resumption applies only to the `new_task` functionality, not general mode switching


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#how-it-works "Direct link to How It Works")
When the `switch_mode` tool is invoked, it follows this process:
  1. **Request Validation** :
     * Validates that the requested mode exists in the system
     * Checks that the `mode_slug` parameter is provided and valid
     * Verifies the user isn't already in the requested mode
     * Ensures the `reason` parameter (if provided) is properly formatted
  2. **Mode Transition Preparation** :
     * Packages the mode change request with the provided reason
     * Presents the change request to the user for approval
  3. **Mode Activation (Upon User Approval)** :
     * Updates the UI to reflect the new mode
     * Adjusts available tools based on the mode's tool group configuration
     * Applies the mode-specific prompt and behavior
     * Applies a 500ms delay to allow the change to take effect before executing next tool
     * Enforces any file restrictions specific to the mode
  4. **Continuation** :
     * Proceeds with the task using the capabilities of the new mode
     * Retains relevant context from the previous interaction


## Tool Group Association[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#tool-group-association "Direct link to Tool Group Association")
The `switch_mode` tool belongs to the "modes" tool group but is also included in the "always available" tools list. This means:
  * It can be used in any mode regardless of the mode's configured tool groups
  * It's available alongside other core tools like `ask_followup_question` and `attempt_completion`
  * It allows mode transitions at any point in a workflow when task requirements change


## Mode Structure[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#mode-structure "Direct link to Mode Structure")
Each mode in the system has a specific structure:
  * `slug`: Unique identifier for the mode (e.g., "code", "ask")
  * `name`: Display name for the mode (e.g., "Code", "Ask")
  * `roleDefinition`: The specialized role and capabilities of the mode
  * `customInstructions`: Optional mode-specific instructions that guide behavior
  * `groups`: Tool groups available to the mode with optional restrictions


## Mode Capabilities[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#mode-capabilities "Direct link to Mode Capabilities")
The core modes provide these specialized capabilities:
  * **Code Mode** : Focused on coding tasks with full access to code editing tools
  * **Architect Mode** : Specialized for system design and architecture planning, limited to editing markdown files only
  * **Ask Mode** : Optimized for answering questions and providing information
  * **Debug Mode** : Equipped for systematic problem diagnosis and resolution


## Custom Modes[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#custom-modes "Direct link to Custom Modes")
Beyond the core modes, the system supports custom project-specific modes:
  * Custom modes can be defined with specific tool groups enabled
  * They can specify custom role definitions and instructions
  * The system checks custom modes first before falling back to core modes
  * Custom mode definitions take precedence over core modes with the same slug


## File Restrictions[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#file-restrictions "Direct link to File Restrictions")
Different modes may have specific file type restrictions:
  * **Architect Mode** : Can only edit files matching the `.md` extension
  * Attempting to edit restricted file types results in a `FileRestrictionError`
  * These restrictions help enforce proper separation of concerns between modes


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#examples-when-used "Direct link to Examples When Used")
  * When discussing a new feature, Roo switches from Ask mode to Architect mode to help design the system structure.
  * After completing architecture planning in Architect mode, Roo switches to Code mode to implement the designed features.
  * When encountering bugs during development, Roo switches from Code mode to Debug mode for systematic troubleshooting.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#usage-examples "Direct link to Usage Examples")
Switching to Code mode for implementation:
```
<switch_mode><mode_slug>code</mode_slug><reason>Need to implement the login functionality based on the architecture we've discussed</reason></switch_mode>
```

Switching to Architect mode for design:
```
<switch_mode><mode_slug>architect</mode_slug><reason>Need to design the system architecture before implementation</reason></switch_mode>
```

Switching to Debug mode for troubleshooting:
```
<switch_mode><mode_slug>debug</mode_slug><reason>Need to systematically diagnose the authentication error</reason></switch_mode>
```

Switching to Ask mode for information:
```
<switch_mode><mode_slug>ask</mode_slug><reason>Need to answer questions about the implemented feature</reason></switch_mode>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/switch-mode.md)
Last updated on **May 27, 2025**
[ Previoussearch_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)[Nextuse_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#how-it-works)
  * [Tool Group Association](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#tool-group-association)
  * [Mode Structure](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#mode-structure)
  * [Mode Capabilities](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#mode-capabilities)
  * [Custom Modes](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#custom-modes)
  * [File Restrictions](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#file-restrictions)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/switch-mode#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fswitch-mode)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



