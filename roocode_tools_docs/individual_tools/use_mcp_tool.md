# use_mcp_tool Tool Documentation

*Tool: use_mcp_tool*
*Source: https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool*
*Scraped: 2025-06-03 14:39:54*
*Content length: 14865 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * use_mcp_tool


On this page
# use_mcp_tool
The `use_mcp_tool` tool enables interaction with external tools provided by connected Model Context Protocol (MCP) servers. It extends Roo's capabilities with domain-specific functionality through a standardized protocol.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `server_name` (required): The name of the MCP server providing the tool
  * `tool_name` (required): The name of the tool to execute
  * `arguments` (required/optional): A JSON object containing the tool's input parameters, following the tool's input schema. May be optional for tools that require no input.


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#what-it-does "Direct link to What It Does")
This tool allows Roo to access specialized functionality provided by external MCP servers. Each MCP server can offer multiple tools with unique capabilities, extending Roo beyond its built-in functionality. The system validates arguments against schemas, manages server connections, and processes responses of various content types (text, image, resource).
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#when-is-it-used "Direct link to When is it used?")
  * When specialized functionality not available in core tools is needed
  * When domain-specific operations are required
  * When integration with external systems or services is needed
  * When working with data that requires specific processing or analysis
  * When accessing proprietary tools through a standardized interface


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#key-features "Direct link to Key Features")
  * Uses the standardized MCP protocol via the `@modelcontextprotocol/sdk` library
  * Supports multiple transport mechanisms (StdioClientTransport, StreamableHTTPClientTransport and SSEClientTransport)
  * Validates arguments using Zod schema validation on both client and server sides
  * Processes multiple response content types: text, image, and resource references
  * Manages server lifecycle with automatic restarts when server code changes
  * Provides an "always allow" mechanism to bypass approval for trusted tools
  * Works with the companion `access_mcp_resource` tool for resource retrieval
  * Maintains proper error tracking and handling for failed operations
  * Supports configurable timeouts (1-3600 seconds, default: 60 seconds)
  * Allows file watchers to automatically detect and reload server changes


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#limitations "Direct link to Limitations")
  * Depends on external MCP servers being available and connected
  * Limited to the tools provided by connected servers
  * Tool capabilities vary between different MCP servers
  * Network issues can affect reliability and performance
  * Requires user approval before execution (unless in the "always allow" list)
  * Cannot execute multiple MCP tool operations simultaneously


## Server Configuration[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#server-configuration "Direct link to Server Configuration")
MCP servers can be configured globally or at the project level:
  * **Global Configuration** : Managed through the Roo Code extension settings in VS Code. These apply across all projects unless overridden.
  * **Project-level Configuration** : Defined in a `.roo/mcp.json` file within your project's root directory.
  * This allows project-specific server setups.
  * Project-level servers take precedence over global servers if they share the same name.
  * Since `.roo/mcp.json` can be committed to version control, it simplifies sharing configurations with your team.


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#how-it-works "Direct link to How It Works")
When the `use_mcp_tool` tool is invoked, it follows this process:
  1. **Initialization and Validation** :
     * The system verifies that the MCP hub is available
     * Confirms the specified server exists and is connected
     * Validates the requested tool exists on the server
     * Arguments are validated against the tool's schema definition
     * Timeout settings are extracted from server configuration (default: 60 seconds)
  2. **Execution and Communication** :
     * The system selects the appropriate transport mechanism: 
       * `StdioClientTransport`: For communicating with local processes via standard I/O
       * `SSEClientTransport`: For communicating with HTTP servers via Server-Sent Events
       * `StreamableHTTPClientTransport`: For communicating with HTTP servers via Streamable HTTP Events
     * A request is sent with validated server name, tool name, and arguments
     * Communication uses the `@modelcontextprotocol/sdk` library for standardized interactions
     * Request execution is tracked with timeout handling to prevent hanging operations
  3. **Response Processing** :
     * Responses can include multiple content types: 
       * Text content: Plain text responses
       * Image content: Binary image data with MIME type information
       * Resource references: URIs to access server resources (works with `access_mcp_resource`)
     * The system checks the `isError` flag to determine if error handling is needed
     * Results are formatted for display in the Roo interface
  4. **Resource and Error Handling** :
     * The system uses WeakRef patterns to prevent memory leaks
     * A consecutive mistake counter tracks and manages errors
     * File watchers monitor for server code changes and trigger automatic restarts
     * The security model requires approval for tool execution unless in the "always allow" list


## Security and Permissions[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#security-and-permissions "Direct link to Security and Permissions")
The MCP architecture provides several security features:
  * Users must approve tool usage before execution (by default)
  * Specific tools can be marked for automatic approval in the "always allow" list
  * Server configurations are validated with Zod schemas for integrity
  * Configurable timeouts prevent hanging operations (1-3600 seconds)
  * Server connections can be enabled or disabled through the UI


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#examples-when-used "Direct link to Examples When Used")
  * Analyzing specialized data formats using server-side processing tools
  * Generating images or other media through AI models hosted on external servers
  * Executing complex domain-specific calculations without local implementation
  * Accessing proprietary APIs or services through a controlled interface
  * Retrieving data from specialized databases or data sources


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#usage-examples "Direct link to Usage Examples")
Requesting weather forecast data with text response:
```
<use_mcp_tool><server_name>weather-server</server_name><tool_name>get_forecast</tool_name><arguments>{ "city": "San Francisco", "days": 5, "format": "text"}</arguments></use_mcp_tool>
```

Analyzing source code with a specialized tool that returns JSON:
```
<use_mcp_tool><server_name>code-analysis</server_name><tool_name>complexity_metrics</tool_name><arguments>{ "language": "typescript", "file_path": "src/app.ts", "include_functions": true, "metrics": ["cyclomatic", "cognitive"]}</arguments></use_mcp_tool>
```

Generating an image with specific parameters:
```
<use_mcp_tool><server_name>image-generation</server_name><tool_name>create_image</tool_name><arguments>{ "prompt": "A futuristic city with flying cars", "style": "photorealistic", "dimensions": {  "width": 1024,  "height": 768 }, "format": "webp"}</arguments></use_mcp_tool>
```

Accessing a resource through a tool that returns a resource reference:
```
<use_mcp_tool><server_name>database-connector</server_name><tool_name>query_and_store</tool_name><arguments>{ "database": "users", "type": "select", "fields": ["name", "email", "last_login"], "where": {  "status": "active" }, "store_as": "active_users"}</arguments></use_mcp_tool>
```

Tool with no required arguments:
```
<use_mcp_tool><server_name>system-monitor</server_name><tool_name>get_current_status</tool_name><arguments>{}</arguments></use_mcp_tool>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/use-mcp-tool.md)
Last updated on **Jun 2, 2025**
[ Previousswitch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)[Nextwrite_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#limitations)
  * [Server Configuration](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#server-configuration)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#how-it-works)
  * [Security and Permissions](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#security-and-permissions)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fuse-mcp-tool)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



