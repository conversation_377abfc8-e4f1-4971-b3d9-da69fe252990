# write_to_file Tool Documentation

*Tool: write_to_file*
*Source: https://docs.roocode.com/advanced-usage/available-tools/write-to-file*
*Scraped: 2025-06-03 14:39:46*
*Content length: 13403 characters*

---

[Skip to main content](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#__docusaurus_skipToContent_fallback)
[![Roo Code Logo](https://docs.roocode.com/img/roo-code-logo-white.png)](https://docs.roocode.com/)
[GitHub](https://github.com/RooCodeInc/Roo-Code)[Install Extension](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
`ctrl``K`
  * [Welcome](https://docs.roocode.com/)
  * [Getting Started](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
  * [Features](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
  * [Advanced Usage](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Available Tools](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
      * [Tool Use Overview](https://docs.roocode.com/advanced-usage/available-tools/tool-use-overview)
      * [access_mcp_resource](https://docs.roocode.com/advanced-usage/available-tools/access-mcp-resource)
      * [apply_diff](https://docs.roocode.com/advanced-usage/available-tools/apply-diff)
      * [ask_followup_question](https://docs.roocode.com/advanced-usage/available-tools/ask-followup-question)
      * [attempt_completion](https://docs.roocode.com/advanced-usage/available-tools/attempt-completion)
      * [browser_action](https://docs.roocode.com/advanced-usage/available-tools/browser-action)
      * [codebase_search](https://docs.roocode.com/advanced-usage/available-tools/codebase-search)
      * [execute_command](https://docs.roocode.com/advanced-usage/available-tools/execute-command)
      * [insert_content](https://docs.roocode.com/advanced-usage/available-tools/insert-content)
      * [list_code_definition_names](https://docs.roocode.com/advanced-usage/available-tools/list-code-definition-names)
      * [list_files](https://docs.roocode.com/advanced-usage/available-tools/list-files)
      * [new_task](https://docs.roocode.com/advanced-usage/available-tools/new-task)
      * [read_file](https://docs.roocode.com/advanced-usage/available-tools/read-file)
      * [search_files](https://docs.roocode.com/advanced-usage/available-tools/search-files)
      * [search_and_replace](https://docs.roocode.com/advanced-usage/available-tools/search-and-replace)
      * [switch_mode](https://docs.roocode.com/advanced-usage/available-tools/switch-mode)
      * [use_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)
      * [write_to_file](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
    * [Context Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
    * [Working with Large Projects](https://docs.roocode.com/advanced-usage/large-projects)
    * [Using Local Models](https://docs.roocode.com/advanced-usage/local-models)
    * [Local Development Setup](https://docs.roocode.com/advanced-usage/local-development-setup)
    * [Prompt Engineering Tips](https://docs.roocode.com/advanced-usage/prompt-engineering)
    * [Prompt Structure](https://docs.roocode.com/advanced-usage/prompt-structure)
    * [Rate Limits and Costs](https://docs.roocode.com/advanced-usage/rate-limits-costs)
    * [Roo Code Nightly](https://docs.roocode.com/advanced-usage/roo-code-nightly)
  * [Model Providers](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
  * [FAQ](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
  * [Tutorial Videos](https://docs.roocode.com/tutorial-videos)
  * [Contributing (GitHub)](https://github.com/RooCodeInc/Roo-Code/blob/main/CONTRIBUTING.md)
  * [Community](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)
  * [Update Notes](https://docs.roocode.com/advanced-usage/available-tools/write-to-file)


  * [](https://docs.roocode.com/)
  * Advanced Usage
  * Available Tools
  * write_to_file


On this page
# write_to_file
The `write_to_file` tool creates new files or completely replaces existing file content with an interactive approval process. It provides a diff view for reviewing changes before they're applied.
## Parameters[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#parameters "Direct link to Parameters")
The tool accepts these parameters:
  * `path` (required): The path of the file to write to, relative to the current working directory
  * `content` (required): The complete content to write to the file
  * `line_count` (required): The number of lines in the file, including empty lines


## What It Does[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#what-it-does "Direct link to What It Does")
This tool writes content to a specified file, either creating a new file if it doesn't exist or completely overwriting an existing file. All changes require explicit user approval through a diff view interface, where users can review and even edit the proposed changes before they're applied.
## When is it used?[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#when-is-it-used "Direct link to When is it used?")
  * When Roo needs to create a new file from scratch
  * When Roo needs to completely rewrite an existing file
  * When creating multiple files for a new project
  * When generating configuration files, documentation, or source code
  * When you need to review changes before they're applied


## Key Features[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#key-features "Direct link to Key Features")
  * Interactive Approval: Shows changes in a diff view requiring explicit approval before applying
  * User Edit Support: Allows editing the proposed content before final approval
  * Safety Measures: Detects code omission, validates paths, and prevents truncated content
  * Editor Integration: Opens a diff view that scrolls to the first difference automatically
  * Content Preprocessing: Handles artifacts from different AI models to ensure clean content
  * Access Control: Validates against `.rooignore` restrictions before making changes
  * Parent Directories: May handle directory creation through system dependencies
  * Complete Replacement: Provides a fully transformed file in a single operation


## Limitations[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#limitations "Direct link to Limitations")
  * Not suitable for existing files: Much slower and less efficient than `apply_diff` for modifying existing files
  * Performance with large files: Operation becomes significantly slower with larger files
  * Complete overwrite: Replaces entire file content, cannot preserve original content
  * Line count required: Needs accurate line count to detect potential content truncation
  * Review overhead: The approval process adds extra steps compared to direct edits
  * Interactive only: Cannot be used in automated workflows that require non-interactive execution


## How It Works[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#how-it-works "Direct link to How It Works")
When the `write_to_file` tool is invoked, it follows this process:
  1. **Parameter Validation** : Validates the required parameters and permissions
     * Checks that `path`, `content`, and `line_count` are provided
     * If `line_count` is missing/invalid, reverts any diff view changes and returns an error suggesting alternative tools (`apply_diff`, `insert_content`, etc.) if modifying an existing file.
     * Validates the file is allowed (not restricted by `.rooignore`)
     * Ensures the path is within the workspace boundaries
     * Tracks consecutive mistake counts for missing parameters
     * Shows specific error messages for each validation failure
  2. **Content Preprocessing** :
     * Removes code block markers that might be added by AI models
     * Handles escaped HTML entities (specifically for non-Claude models)
     * Strips line numbers if accidentally included in content
     * Performs model-specific processing for different AI providers
  3. **Diff View Generation** :
     * Opens a diff view in the editor showing the proposed changes
     * Adds a 300ms delay to ensure UI responsiveness
     * Scrolls automatically to the first difference
     * Highlights changes for easy review
  4. **User Approval Process** :
     * Waits for explicit user approval to proceed
     * Allows users to edit the content in the diff view
     * Captures any user edits for the final content
     * Provides option to reject changes entirely
     * Detects and incorporates user modifications into the final result
  5. **Safety Validation** :
     * Detects potential content truncation by comparing with provided line count
     * Shows warnings if content appears incomplete
     * Validates file path and access permissions
     * Specifically checks if files are outside the workspace with `isOutsideWorkspace` flag
  6. **File Writing** :
     * Writes the approved content (with any user edits) to the file
     * Provides confirmation of successful write
     * Resets the consecutive mistakes counter on success


## Examples When Used[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#examples-when-used "Direct link to Examples When Used")
  * When creating a new project, Roo generates multiple files but lets you review each before committing changes.
  * When setting up configuration files, Roo shows the proposed configuration in a diff view for approval.
  * When generating documentation, Roo creates markdown files but lets you make final adjustments in the diff view.
  * When developing a prototype, Roo shows complete source files in a diff view where you can fine-tune before saving.


## Usage Examples[​](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#usage-examples "Direct link to Usage Examples")
Creating a new JSON configuration file:
```
<write_to_file><path>config/settings.json</path><content>{ "apiEndpoint": "https://api.example.com", "theme": {  "primaryColor": "#007bff",  "secondaryColor": "#6c757d",  "fontFamily": "Arial, sans-serif" }, "features": {  "darkMode": true,  "notifications": true,  "analytics": false }, "version": "1.0.0"}</content><line_count>14</line_count></write_to_file>
```

Creating a simple HTML file:
```
<write_to_file><path>src/index.html</path><content><!DOCTYPE html><html lang="en"><head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>My Application</title> <link rel="stylesheet" href="styles.css"></head><body> <div id="app"></div> <script src="app.js"></script></body></html></content><line_count>13</line_count></write_to_file>
```

Creating a JavaScript module:
```
<write_to_file><path>src/utils/helpers.js</path><content>/** * Utility functions for the application */export function formatDate(date) { return new Date(date).toLocaleDateString();}export function calculateTotal(items) { return items.reduce((sum, item) => sum + item.price, 0);}export function debounce(func, delay) { let timeout; return function(...args) {  clearTimeout(timeout);  timeout = setTimeout(() => func.apply(this, args), delay); };}</content><line_count>18</line_count></write_to_file>
```

[Edit this page](https://github.com/RooCodeInc/Roo-Code-Docs/edit/main/docs/advanced-usage/available-tools/write-to-file.md)
Last updated on **May 27, 2025**
[ Previoususe_mcp_tool](https://docs.roocode.com/advanced-usage/available-tools/use-mcp-tool)[NextContext Poisoning](https://docs.roocode.com/advanced-usage/context-poisoning)
  * [Parameters](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#parameters)
  * [What It Does](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#what-it-does)
  * [When is it used?](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#when-is-it-used)
  * [Key Features](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#key-features)
  * [Limitations](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#limitations)
  * [How It Works](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#how-it-works)
  * [Examples When Used](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#examples-when-used)
  * [Usage Examples](https://docs.roocode.com/advanced-usage/available-tools/write-to-file#usage-examples)


Is this documentation incorrect or incomplete? [Report an issue on GitHub](https://github.com/RooCodeInc/Roo-Code-Docs/issues/new?title=Documentation%20Issue:%20%2Fadvanced-usage%2Favailable-tools%2Fwrite-to-file)
Community
  * [Discord](https://discord.gg/roocode)
  * [Reddit](https://www.reddit.com/r/RooCode/)
  * [Twitter](https://x.com/roo_code)


GitHub
  * [Issues](https://github.com/RooCodeInc/Roo-Code/issues)
  * [Feature Requests](https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)


Download
  * [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
  * [Open VSX Registry](https://open-vsx.org/extension/RooVeterinaryInc/roo-cline)


Company
  * Contact
  * [Careers](https://careers.roocode.com)
  * [Website Privacy Policy](https://roocode.com/privacy)
  * [Extension Privacy Policy](https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md)



