"""
Analysis package for the betting project.
This package handles all analysis functionality including feature importance,
SHAP analysis, and visualization.
"""

from .core import (
    perform_model_analysis,
    analyze_prediction_results,
    analyze_feature_relationships
)
from .feature_importance import (
    analyze_feature_importance,
    analyze_permutation_importance,
    analyze_feature_interactions
)
from .shap_analysis import (
    perform_shap_analysis,
    analyze_shap_interactions
)
from .visualization import (
    plot_confusion_matrix,
    plot_correlation_matrix,
    plot_feature_distributions,
    plot_prediction_distribution,
    plot_feature_importance_bar,
    set_plot_style
)

__all__ = [
    # Core analysis functionality
    'perform_model_analysis',
    'analyze_prediction_results',
    'analyze_feature_relationships',
    
    # Feature importance analysis
    'analyze_feature_importance',
    'analyze_permutation_importance',
    'analyze_feature_interactions',
    
    # SHAP analysis
    'perform_shap_analysis',
    'analyze_shap_interactions',
    
    # Visualization functions
    'plot_confusion_matrix',
    'plot_correlation_matrix',
    'plot_feature_distributions',
    'plot_prediction_distribution',
    'plot_feature_importance_bar',
    'set_plot_style'
]
