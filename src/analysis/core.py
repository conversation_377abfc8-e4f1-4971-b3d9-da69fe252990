"""
Core analysis functionality that coordinates all analysis components.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from sklearn.ensemble import RandomForestClassifier

from .feature_importance import (
    analyze_feature_importance,
    analyze_permutation_importance,
    analyze_feature_interactions
)
from .shap_analysis import (
    perform_shap_analysis,
    analyze_shap_interactions
)
from .visualization import (
    plot_confusion_matrix,
    plot_correlation_matrix,
    plot_feature_distributions,
    plot_prediction_distribution,
    set_plot_style
)

logger = logging.getLogger(__name__)

def perform_model_analysis(
    model: RandomForestClassifier,
    X: pd.DataFrame,
    y: pd.Series,
    feature_names: List[str],
    pred_type: str,
    output_dir: str = "analysis_output"
) -> Dict[str, Any]:
    """
    Perform comprehensive model analysis.

    Args:
        model: Trained model
        X: Feature matrix
        y: Target vector
        feature_names: List of feature names
        pred_type: Type of prediction
        output_dir: Directory to save analysis outputs

    Returns:
        Dictionary containing all analysis results
    """
    try:
        import os
        os.makedirs(output_dir, exist_ok=True)
        set_plot_style()

        results = {}

        # Feature importance analysis
        logger.info("Performing feature importance analysis...")
        results['feature_importance'] = analyze_feature_importance(
            model, X, y, feature_names, pred_type
        )

        # Permutation importance analysis
        logger.info("Performing permutation importance analysis...")
        results['permutation_importance'] = analyze_permutation_importance(
            model, X, y, feature_names, pred_type
        )

        # Feature interactions analysis
        logger.info("Analyzing feature interactions...")
        results['feature_interactions'] = analyze_feature_interactions(
            model, X, feature_names
        )

        # SHAP analysis
        logger.info("Performing SHAP analysis...")
        results['shap_analysis'] = perform_shap_analysis(
            model, X, feature_names
        )

        # SHAP interactions
        logger.info("Analyzing SHAP interactions...")
        results['shap_interactions'] = analyze_shap_interactions(
            model, X, feature_names
        )

        # Additional visualizations
        _create_additional_visualizations(
            X, y, model, feature_names, pred_type, output_dir
        )

        return results

    except Exception as e:
        logger.error(f"Error performing model analysis: {str(e)}")
        return {}

def analyze_prediction_results(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    y_prob: np.ndarray,
    classes: List[str],
    pred_type: str,
    output_dir: str = "analysis_output"
) -> Dict[str, Any]:
    """
    Analyze prediction results.

    Args:
        y_true: True labels
        y_pred: Predicted labels
        y_prob: Prediction probabilities
        classes: List of class names
        pred_type: Type of prediction
        output_dir: Directory to save analysis outputs

    Returns:
        Dictionary containing analysis results
    """
    try:
        import os
        os.makedirs(output_dir, exist_ok=True)
        results = {}

        # Confusion matrix
        logger.info("Creating confusion matrix...")
        plot_confusion_matrix(
            y_true,
            y_pred,
            classes,
            pred_type
        )

        # Prediction distribution
        logger.info("Analyzing prediction distribution...")
        plot_prediction_distribution(
            y_pred,
            classes,
            pred_type,
            os.path.join(output_dir, f"prediction_distribution_{pred_type}.png")
        )

        # Calculate metrics
        results['metrics'] = _calculate_prediction_metrics(
            y_true, y_pred, y_prob, classes
        )

        # Analyze prediction confidence
        results['confidence_analysis'] = _analyze_prediction_confidence(
            y_true, y_pred, y_prob, classes
        )

        return results

    except Exception as e:
        logger.error(f"Error analyzing prediction results: {str(e)}")
        return {}

def analyze_feature_relationships(
    X: pd.DataFrame,
    feature_names: List[str],
    output_dir: str = "analysis_output"
) -> Dict[str, Any]:
    """
    Analyze relationships between features.

    Args:
        X: Feature matrix
        feature_names: List of feature names
        output_dir: Directory to save analysis outputs

    Returns:
        Dictionary containing analysis results
    """
    try:
        import os
        os.makedirs(output_dir, exist_ok=True)
        results = {}

        # Correlation analysis
        logger.info("Analyzing feature correlations...")
        plot_correlation_matrix(
            X.values,
            feature_names,
            os.path.join(output_dir, "feature_correlations.png")
        )

        # Feature distributions
        logger.info("Analyzing feature distributions...")
        plot_feature_distributions(
            X.values,
            feature_names,
            os.path.join(output_dir, "feature_distributions.png")
        )

        # Calculate correlation statistics
        results['correlation_stats'] = _calculate_correlation_statistics(X)

        return results

    except Exception as e:
        logger.error(f"Error analyzing feature relationships: {str(e)}")
        return {}

def _create_additional_visualizations(
    X: pd.DataFrame,
    y: pd.Series,
    model: RandomForestClassifier,
    feature_names: List[str],
    pred_type: str,
    output_dir: str
) -> None:
    """Create additional analysis visualizations."""
    try:
        # Feature distributions by class
        if len(np.unique(y)) <= 5:  # Only for reasonable number of classes
            for feature in feature_names:
                plt.figure(figsize=(10, 6))
                for class_label in np.unique(y):
                    class_data = X[feature][y == class_label]
                    plt.hist(
                        class_data,
                        alpha=0.5,
                        label=f'Class {class_label}',
                        bins=30
                    )
                plt.title(f'{feature} Distribution by Class')
                plt.xlabel(feature)
                plt.ylabel('Count')
                plt.legend()
                plt.savefig(
                    os.path.join(output_dir, f"{feature}_by_class.png")
                )
                plt.close()

    except Exception as e:
        logger.error(f"Error creating additional visualizations: {str(e)}")

def _calculate_prediction_metrics(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    y_prob: np.ndarray,
    classes: List[str]
) -> Dict[str, float]:
    """Calculate comprehensive prediction metrics."""
    from sklearn.metrics import (
        accuracy_score,
        precision_score,
        recall_score,
        f1_score,
        roc_auc_score,
        log_loss
    )

    metrics = {
        'accuracy': accuracy_score(y_true, y_pred),
        'macro_precision': precision_score(y_true, y_pred, average='macro'),
        'macro_recall': recall_score(y_true, y_pred, average='macro'),
        'macro_f1': f1_score(y_true, y_pred, average='macro'),
    }

    # Add ROC AUC if applicable
    if y_prob.shape[1] == 2:  # Binary classification
        metrics['roc_auc'] = roc_auc_score(y_true, y_prob[:, 1])
    elif y_prob.shape[1] > 2:  # Multi-class
        metrics['roc_auc'] = roc_auc_score(y_true, y_prob, multi_class='ovr')

    # Add log loss
    metrics['log_loss'] = log_loss(y_true, y_prob)

    return metrics

def _analyze_prediction_confidence(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    y_prob: np.ndarray,
    classes: List[str]
) -> Dict[str, Any]:
    """Analyze prediction confidence patterns."""
    confidence_analysis = {
        'mean_confidence': float(np.mean(np.max(y_prob, axis=1))),
        'std_confidence': float(np.std(np.max(y_prob, axis=1))),
    }

    # Analyze confidence by class
    confidence_by_class = {}
    for i, class_name in enumerate(classes):
        class_mask = y_true == i
        if np.any(class_mask):
            confidence_by_class[class_name] = {
                'mean': float(np.mean(y_prob[class_mask, i])),
                'std': float(np.std(y_prob[class_mask, i])),
            }

    confidence_analysis['by_class'] = confidence_by_class
    return confidence_analysis

def _calculate_correlation_statistics(X: pd.DataFrame) -> Dict[str, Any]:
    """Calculate correlation statistics for features."""
    corr_matrix = X.corr()
    
    stats = {
        'highest_correlation': {
            'features': None,
            'value': 0.0
        },
        'mean_absolute_correlation': float(
            np.mean(np.abs(np.triu(corr_matrix.values, k=1)))
        ),
        'highly_correlated_pairs': []
    }

    # Find highest correlation and highly correlated pairs
    n_features = len(X.columns)
    for i in range(n_features):
        for j in range(i + 1, n_features):
            corr_value = abs(corr_matrix.iloc[i, j])
            if corr_value > abs(stats['highest_correlation']['value']):
                stats['highest_correlation'] = {
                    'features': (X.columns[i], X.columns[j]),
                    'value': float(corr_value)
                }
            if corr_value > 0.8:  # Threshold for high correlation
                stats['highly_correlated_pairs'].append({
                    'features': (X.columns[i], X.columns[j]),
                    'correlation': float(corr_value)
                })

    return stats
