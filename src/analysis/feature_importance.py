"""
Feature importance analysis functionality.
"""

import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Any, Union
from sklearn.ensemble import RandomForestClassifier
from sklearn.calibration import CalibratedClassifierCV
from .visualization import plot_feature_importance_bar
from .constants import LOG_FORMATS

logger = logging.getLogger(__name__)

def analyze_feature_importance(
    model: Union[RandomForestClassifier, CalibratedClassifierCV, Any],
    X: pd.DataFrame,
    y: pd.Series,
    feature_names: List[str],
    pred_type: str
) -> Optional[Dict[str, float]]:
    """
    Analyze and visualize feature importance.

    Args:
        model: Trained model
        X: Feature matrix
        y: Target vector
        feature_names: List of feature names
        pred_type: Type of prediction

    Returns:
        Dictionary mapping feature names to importance scores, or None if analysis fails
    """
    try:
        # Handle different model types
        if isinstance(model, CalibratedClassifierCV):
            base_estimator = model.base_estimator
            if hasattr(base_estimator, 'feature_importances_'):
                importances = base_estimator.feature_importances_
            else:
                logger.warning(f"Base estimator for {pred_type} does not support feature importance")
                return None
        elif hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
        else:
            logger.warning(f"Model for {pred_type} does not support feature importance")
            return None
        
        # Create visualization
        plot_feature_importance_bar(importances, feature_names, pred_type)

        # Log feature importances
        _log_feature_importances(feature_names, importances, pred_type)

        # Return feature importance dictionary
        return dict(zip(feature_names, importances))

    except Exception as e:
        logger.error(f"Error analyzing feature importance: {str(e)}")
        return None

def analyze_permutation_importance(
    model: Union[RandomForestClassifier, CalibratedClassifierCV, Any],
    X: pd.DataFrame,
    y: pd.Series,
    feature_names: List[str],
    pred_type: str,
    n_repeats: int = 10
) -> Optional[Dict[str, Dict[str, float]]]:
    """
    Analyze feature importance using permutation importance method.

    Args:
        model: Trained model
        X: Feature matrix
        y: Target vector
        feature_names: List of feature names
        pred_type: Type of prediction
        n_repeats: Number of times to repeat permutation

    Returns:
        Dictionary containing mean and std of importance scores, or None if analysis fails
    """
    try:
        from sklearn.inspection import permutation_importance
        
        # Ensure feature names match
        if not all(col in X.columns for col in feature_names):
            missing_features = [f for f in feature_names if f not in X.columns]
            logger.error(f"Missing features in X: {missing_features}")
            return None

        # Select only the features we want to analyze
        X = X[feature_names]
        
        # Calculate permutation importance
        r = permutation_importance(
            model, X, y,
            n_repeats=n_repeats,
            random_state=42
        )

        # Create results dictionary
        results = {
            feature: {
                'mean_importance': mean,
                'std_importance': std
            }
            for feature, mean, std in zip(
                feature_names,
                r.importances_mean,
                r.importances_std
            )
        }

        # Sort features by mean importance
        sorted_features = sorted(
            results.items(),
            key=lambda x: x[1]['mean_importance'],
            reverse=True
        )

        # Log results
        logger.info(f"\nPermutation Importance Analysis for {pred_type}:")
        for feature, scores in sorted_features:
            logger.info(
                f"{feature}: "
                f"Mean={scores['mean_importance']:.4f} ± "
                f"Std={scores['std_importance']:.4f}"
            )

        return results

    except Exception as e:
        logger.error(f"Error analyzing permutation importance: {str(e)}")
        return None

def analyze_feature_interactions(
    model: Union[RandomForestClassifier, CalibratedClassifierCV, Any],
    X: pd.DataFrame,
    feature_names: List[str],
    top_k: int = 10
) -> Optional[Dict[str, float]]:
    """
    Analyze feature interactions using mean decrease in impurity.

    Args:
        model: Trained model
        X: Feature matrix
        feature_names: List of feature names
        top_k: Number of top interactions to analyze

    Returns:
        Dictionary mapping feature pairs to interaction scores, or None if analysis fails
    """
    try:
        # Handle different model types
        if isinstance(model, CalibratedClassifierCV):
            base_estimator = model.base_estimator
            if not hasattr(base_estimator, 'estimators_'):
                logger.warning("Base estimator does not support feature interactions")
                return None
            estimators = base_estimator.estimators_
        elif hasattr(model, 'estimators_'):
            estimators = model.estimators_
        else:
            logger.warning("Model does not support feature interactions")
            return None

        n_features = len(feature_names)
        interactions = np.zeros((n_features, n_features))

        # Calculate interaction scores
        for tree in estimators:
            _process_tree_interactions(tree, interactions)

        # Convert to dictionary of feature pairs
        interaction_dict = {}
        for i in range(n_features):
            for j in range(i + 1, n_features):
                if interactions[i, j] > 0:
                    pair = f"{feature_names[i]} × {feature_names[j]}"
                    interaction_dict[pair] = float(interactions[i, j])

        # Sort and get top k interactions
        sorted_interactions = dict(
            sorted(
                interaction_dict.items(),
                key=lambda x: x[1],
                reverse=True
            )[:top_k]
        )

        # Log results
        logger.info("\nTop Feature Interactions:")
        for pair, score in sorted_interactions.items():
            logger.info(f"{pair}: {score:.4f}")

        return sorted_interactions

    except Exception as e:
        logger.error(f"Error analyzing feature interactions: {str(e)}")
        return None

def _log_feature_importances(
    feature_names: List[str],
    importances: np.ndarray,
    pred_type: str
) -> None:
    """Log feature importance scores."""
    logger.info(LOG_FORMATS['feature_importance'].format(pred_type))
    for f, imp in sorted(
        zip(feature_names, importances),
        key=lambda x: x[1],
        reverse=True
    ):
        logger.info(LOG_FORMATS['feature_value'].format(f, imp))

def _process_tree_interactions(
    tree,
    interactions: np.ndarray
) -> None:
    """Process a single tree for feature interactions."""
    def _walk_tree(node_id, path):
        if tree.tree_.children_left[node_id] == -1:  # Leaf node
            return
        
        feature = tree.tree_.feature[node_id]
        improvement = tree.tree_.impurity[node_id] - (
            tree.tree_.impurity[tree.tree_.children_left[node_id]] +
            tree.tree_.impurity[tree.tree_.children_right[node_id]]
        ) / 2.0

        # Update interaction scores for feature pairs in path
        for prev_feature in path:
            interactions[prev_feature, feature] += improvement
            interactions[feature, prev_feature] += improvement

        # Recursively process children
        path.add(feature)
        _walk_tree(tree.tree_.children_left[node_id], path)
        _walk_tree(tree.tree_.children_right[node_id], path)
        path.remove(feature)

    _walk_tree(0, set())
