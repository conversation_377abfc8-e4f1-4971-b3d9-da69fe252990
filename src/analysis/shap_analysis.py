"""
SHAP (SHapley Additive exPlanations) analysis functionality.
"""

import logging
import shap
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union, Any
from sklearn.ensemble import RandomForestClassifier
from .constants import SHAP_FIG_SIZE, PLOT_SETTINGS, OUTPUT_PATHS

logger = logging.getLogger(__name__)

def perform_shap_analysis(
    model: RandomForestClassifier,
    X: pd.DataFrame,
    feature_names: List[str],
    sample_size: Optional[int] = None
) -> Optional[Dict[str, np.ndarray]]:
    """
    Perform SHAP analysis and create visualizations.

    Args:
        model: Trained model
        X: Feature matrix
        feature_names: List of feature names
        sample_size: Number of samples to use for SHAP analysis (optional)

    Returns:
        Dictionary containing SHAP values and summary statistics, or None if analysis fails
    """
    try:
        # Sample data if specified
        if sample_size is not None and sample_size < len(X):
            X_sample = X.sample(n=sample_size, random_state=42)
        else:
            X_sample = X

        # Create SHAP explainer
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_sample)

        # Create visualizations
        _create_shap_visualizations(shap_values, X_sample, feature_names)

        # Calculate and log SHAP summary statistics
        summary_stats = _calculate_shap_summary(shap_values, feature_names)
        _log_shap_summary(summary_stats)

        return {
            'shap_values': shap_values,
            'summary_stats': summary_stats
        }

    except Exception as e:
        logger.error(f"Error performing SHAP analysis: {str(e)}")
        return None

def analyze_shap_interactions(
    model: RandomForestClassifier,
    X: pd.DataFrame,
    feature_names: List[str],
    top_k: int = 10
) -> Optional[Dict[str, float]]:
    """
    Analyze feature interactions using SHAP interaction values.

    Args:
        model: Trained model
        X: Feature matrix
        feature_names: List of feature names
        top_k: Number of top interactions to analyze

    Returns:
        Dictionary of top feature interactions and their scores, or None if analysis fails
    """
    try:
        # Create explainer and calculate interaction values
        explainer = shap.TreeExplainer(model)
        shap_interaction_values = explainer.shap_interaction_values(X)

        # Calculate interaction strengths
        interaction_strengths = _calculate_interaction_strengths(
            shap_interaction_values,
            feature_names
        )

        # Get top interactions
        top_interactions = dict(
            sorted(
                interaction_strengths.items(),
                key=lambda x: abs(x[1]),
                reverse=True
            )[:top_k]
        )

        # Log results
        logger.info("\nTop SHAP Feature Interactions:")
        for pair, strength in top_interactions.items():
            logger.info(f"{pair}: {strength:.4f}")

        return top_interactions

    except Exception as e:
        logger.error(f"Error analyzing SHAP interactions: {str(e)}")
        return None

def _create_shap_visualizations(
    shap_values: Union[np.ndarray, List[np.ndarray]],
    X: pd.DataFrame,
    feature_names: List[str]
) -> None:
    """Create various SHAP visualizations."""
    try:
        # Summary plot
        plt.figure(figsize=SHAP_FIG_SIZE)
        shap.summary_plot(
            shap_values,
            X,
            plot_type=PLOT_SETTINGS['shap']['plot_type'],
            feature_names=feature_names,
            show=False
        )
        plt.title(PLOT_SETTINGS['shap']['title'])
        plt.tight_layout()
        plt.savefig(OUTPUT_PATHS['shap'])
        plt.close()

        # Additional visualizations for multi-class problems
        if isinstance(shap_values, list):
            _create_multiclass_visualizations(shap_values, X, feature_names)

    except Exception as e:
        logger.error(f"Error creating SHAP visualizations: {str(e)}")

def _create_multiclass_visualizations(
    shap_values: List[np.ndarray],
    X: pd.DataFrame,
    feature_names: List[str]
) -> None:
    """Create visualizations for multi-class problems."""
    try:
        for i, class_shap_values in enumerate(shap_values):
            plt.figure(figsize=SHAP_FIG_SIZE)
            shap.summary_plot(
                class_shap_values,
                X,
                plot_type="bar",
                feature_names=feature_names,
                show=False
            )
            plt.title(f"SHAP Feature Importance - Class {i}")
            plt.tight_layout()
            plt.savefig(f"shap_feature_importance_class_{i}.png")
            plt.close()

    except Exception as e:
        logger.error(f"Error creating multi-class SHAP visualizations: {str(e)}")

def _calculate_shap_summary(
    shap_values: Union[np.ndarray, List[np.ndarray]],
    feature_names: List[str]
) -> Dict[str, Dict[str, float]]:
    """Calculate summary statistics from SHAP values."""
    try:
        if isinstance(shap_values, list):
            # Multi-class case
            summary = {}
            for i, class_shap_values in enumerate(shap_values):
                class_summary = _calculate_class_summary(class_shap_values, feature_names)
                summary[f'class_{i}'] = class_summary
            return summary
        else:
            # Binary classification case
            return _calculate_class_summary(shap_values, feature_names)

    except Exception as e:
        logger.error(f"Error calculating SHAP summary: {str(e)}")
        return {}

def _calculate_class_summary(
    shap_values: np.ndarray,
    feature_names: List[str]
) -> Dict[str, Dict[str, float]]:
    """Calculate summary statistics for a single class."""
    summary = {}
    for i, feature in enumerate(feature_names):
        feature_shap = shap_values[:, i]
        summary[feature] = {
            'mean_abs_shap': float(np.mean(np.abs(feature_shap))),
            'mean_shap': float(np.mean(feature_shap)),
            'std_shap': float(np.std(feature_shap)),
            'max_abs_shap': float(np.max(np.abs(feature_shap)))
        }
    return summary

def _calculate_interaction_strengths(
    interaction_values: np.ndarray,
    feature_names: List[str]
) -> Dict[str, float]:
    """Calculate interaction strengths from SHAP interaction values."""
    n_features = len(feature_names)
    interaction_strengths = {}

    for i in range(n_features):
        for j in range(i + 1, n_features):
            # Calculate mean absolute interaction value
            strength = np.mean(np.abs(interaction_values[:, i, j]))
            if strength > 0:
                pair = f"{feature_names[i]} × {feature_names[j]}"
                interaction_strengths[pair] = float(strength)

    return interaction_strengths

def _log_shap_summary(summary: Dict[str, Any]) -> None:
    """Log SHAP analysis summary."""
    logger.info("\nSHAP Analysis Summary:")
    
    if 'class_0' in summary:
        # Multi-class case
        for class_name, class_summary in summary.items():
            logger.info(f"\n{class_name.upper()}:")
            _log_class_summary(class_summary)
    else:
        # Binary classification case
        _log_class_summary(summary)

def _log_class_summary(class_summary: Dict[str, Dict[str, float]]) -> None:
    """Log summary for a single class."""
    for feature, stats in sorted(
        class_summary.items(),
        key=lambda x: x[1]['mean_abs_shap'],
        reverse=True
    ):
        logger.info(
            f"{feature}:"
            f" Mean Abs SHAP={stats['mean_abs_shap']:.4f},"
            f" Mean SHAP={stats['mean_shap']:.4f},"
            f" Std SHAP={stats['std_shap']:.4f},"
            f" Max Abs SHAP={stats['max_abs_shap']:.4f}"
        )
