"""
Visualization functionality for analysis results.
"""

import logging
import matplotlib
matplotlib.use('Agg')  # Use Agg backend to avoid display issues
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from typing import List
from sklearn.metrics import confusion_matrix
from .constants import (
    FEATURE_IMPORTANCE_FIG_SIZE,
    CONFUSION_MATRIX_FIG_SIZE,
    PLOT_SETTINGS,
    OUTPUT_PATHS,
    COLOR_SCHEMES
)

logger = logging.getLogger(__name__)

def plot_feature_importance_bar(
    importances: np.ndarray,
    feature_names: List[str],
    pred_type: str
) -> None:
    """
    Create and save feature importance bar plot.

    Args:
        importances: Array of feature importance scores
        feature_names: List of feature names
        pred_type: Type of prediction
    """
    try:
        # Set style
        plt.style.use('default')  # Use default style instead of seaborn
        
        indices = np.argsort(importances)[::-1]
        settings = PLOT_SETTINGS['feature_importance']

        plt.figure(figsize=FEATURE_IMPORTANCE_FIG_SIZE)
        plt.title(settings['title_template'].format(pred_type))
        plt.bar(range(len(importances)), importances[indices])
        plt.xticks(
            range(len(importances)),
            [feature_names[i] for i in indices],
            rotation=settings['rotation']
        )
        plt.tight_layout()
        plt.savefig(OUTPUT_PATHS['feature_importance'].format(pred_type))
        plt.close()

    except Exception as e:
        logger.error(f"Error plotting feature importance: {str(e)}")

def plot_confusion_matrix(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    classes: List[str],
    pred_type: str
) -> None:
    """
    Create and save confusion matrix plot.

    Args:
        y_true: True labels
        y_pred: Predicted labels
        classes: List of class names
        pred_type: Type of prediction
    """
    try:
        # Set style
        plt.style.use('default')
        
        cm = confusion_matrix(y_true, y_pred)
        settings = PLOT_SETTINGS['confusion_matrix']

        plt.figure(figsize=CONFUSION_MATRIX_FIG_SIZE)
        sns.heatmap(
            cm,
            annot=True,
            fmt=settings['fmt'],
            cmap='YlOrRd',  # Use a standard colormap
            xticklabels=classes,
            yticklabels=classes
        )
        plt.title(settings['title_template'].format(pred_type))
        plt.xlabel("Predicted")
        plt.ylabel("True")
        plt.tight_layout()
        plt.savefig(OUTPUT_PATHS['confusion_matrix'].format(pred_type))
        plt.close()

    except Exception as e:
        logger.error(f"Error plotting confusion matrix: {str(e)}")

def plot_correlation_matrix(
    data: np.ndarray,
    feature_names: List[str],
    output_path: str
) -> None:
    """
    Create and save correlation matrix plot.

    Args:
        data: Feature matrix
        feature_names: List of feature names
        output_path: Path to save the plot
    """
    try:
        # Set style
        plt.style.use('default')
        
        corr = np.corrcoef(data.T)
        
        plt.figure(figsize=FEATURE_IMPORTANCE_FIG_SIZE)
        sns.heatmap(
            corr,
            annot=True,
            fmt='.2f',
            cmap='coolwarm',  # Use a standard colormap
            xticklabels=feature_names,
            yticklabels=feature_names
        )
        plt.title("Feature Correlation Matrix")
        plt.xticks(rotation=90)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

    except Exception as e:
        logger.error(f"Error plotting correlation matrix: {str(e)}")

def plot_feature_distributions(
    data: np.ndarray,
    feature_names: List[str],
    output_path: str
) -> None:
    """
    Create and save feature distribution plots.

    Args:
        data: Feature matrix
        feature_names: List of feature names
        output_path: Path to save the plot
    """
    try:
        # Set style
        plt.style.use('default')
        
        n_features = data.shape[1]
        n_cols = 3
        n_rows = (n_features + n_cols - 1) // n_cols

        plt.figure(figsize=(15, 5 * n_rows))
        for i in range(n_features):
            plt.subplot(n_rows, n_cols, i + 1)
            sns.histplot(data[:, i], kde=True)
            plt.title(feature_names[i])
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

    except Exception as e:
        logger.error(f"Error plotting feature distributions: {str(e)}")

def plot_prediction_distribution(
    predictions: np.ndarray,
    classes: List[str],
    pred_type: str,
    output_path: str
) -> None:
    """
    Create and save prediction distribution plot.

    Args:
        predictions: Array of predictions
        classes: List of class names
        pred_type: Type of prediction
        output_path: Path to save the plot
    """
    try:
        # Set style
        plt.style.use('default')
        
        plt.figure(figsize=(10, 6))
        sns.countplot(x=predictions)
        plt.title(f"Prediction Distribution for {pred_type}")
        plt.xticks(range(len(classes)), classes, rotation=45)
        plt.xlabel("Predicted Class")
        plt.ylabel("Count")
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

    except Exception as e:
        logger.error(f"Error plotting prediction distribution: {str(e)}")

def set_plot_style() -> None:
    """Set consistent style for all plots."""
    try:
        plt.style.use('default')  # Use default style instead of seaborn
        matplotlib.rcParams.update({'font.size': 10})
        matplotlib.rcParams.update({'figure.autolayout': True})
        
    except Exception as e:
        logger.error(f"Error setting plot style: {str(e)}")

def save_plot(fig: plt.Figure, output_path: str, dpi: int = 300) -> None:
    """
    Save plot with error handling.

    Args:
        fig: matplotlib Figure object
        output_path: Path to save the plot
        dpi: Resolution for saved image
    """
    try:
        fig.savefig(output_path, dpi=dpi, bbox_inches='tight')
        plt.close(fig)
        
    except Exception as e:
        logger.error(f"Error saving plot to {output_path}: {str(e)}")
        plt.close(fig)
