"""
This module handles model training for the betting project.
It includes functions to train models for different prediction types and perform cross-validation.
"""

import pandas as pd
import numpy as np
import os
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
import logging
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from utils import get_image_directory, safe_classification_report
import matplotlib.pyplot as plt
import tensorflow as tf
from optuna import create_study
from sklearn.inspection import permutation_importance
import traceback

logger = logging.getLogger(__name__)


def create_three_way_model(input_shape, num_classes):
    """
    Create a neural network model for three-way prediction with balanced architecture.

    Args:
        input_shape (int): Number of input features.
        num_classes (int): Number of output classes.

    Returns:
        tf.keras.Sequential: Compiled neural network model.
    """
    model = tf.keras.Sequential(
        [
            tf.keras.layers.Dense(256, activation="relu", input_shape=(input_shape,)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(128, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(64, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(32, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(num_classes, activation="softmax"),
        ]
    )

    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss="categorical_crossentropy",
        metrics=["accuracy"],
    )
    return model


def train_model(X, y_dict, label_encoders):
    """
    Train multiple models for different prediction types.

    Args:
        X (pd.DataFrame): Feature DataFrame.
        y_dict (dict): Dictionary of target variables for different prediction types.
        label_encoders (dict): Dictionary of label encoders for each prediction type.

    Returns:
        dict: Dictionary of trained models and related information for each prediction type.
    """
    logger.info("Starting train_model function")
    logger.info(f"Shape of X: {X.shape}")

    # Remove string columns and keep track of numeric columns
    string_columns = [
        "Team",
        "Home Team",
        "Away Team",
        "correct_score",
        "form_data_valid_str",
    ]
    numeric_columns = [col for col in X.columns if col not in string_columns]
    X_numeric = X[numeric_columns]

    # Split data chronologically
    train_size = int(0.8 * len(X_numeric))
    X_train = X_numeric[:train_size]
    X_test = X_numeric[train_size:]

    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Convert to DataFrame to preserve column names
    X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns)
    X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns)

    models = {}
    for pred_type, y in y_dict.items():
        logger.info(f"\nTraining model for {pred_type}")

        # Split target variable
        y_train = y[:train_size]
        y_test = y[train_size:]

        # Ensure label encoder exists
        if pred_type not in label_encoders:
            label_encoders[pred_type] = LabelEncoder()
        encoder = label_encoders[pred_type]

        # Fit encoder with all possible values
        unique_values = y.unique()
        if (
            not hasattr(encoder, "classes_")
            or len(set(unique_values) - set(encoder.classes_)) > 0
        ):
            encoder.fit(unique_values)

        y_train_encoded = encoder.transform(y_train)
        y_test_encoded = encoder.transform(y_test)

        try:
            if pred_type == "three_way":
                # Convert to categorical
                y_train_cat = tf.keras.utils.to_categorical(y_train_encoded)
                y_test_cat = tf.keras.utils.to_categorical(y_test_encoded)

                # Create and train model
                model = create_three_way_model(
                    X_train_scaled.shape[1], len(encoder.classes_)
                )

                # Calculate balanced class weights
                class_counts = np.bincount(y_train_encoded)
                total_samples = len(y_train_encoded)
                class_weights = {
                    i: total_samples / (len(class_counts) * count)
                    for i, count in enumerate(class_counts)
                }

                # Adjust weights to give more importance to Draw class
                if "Draw" in encoder.classes_:
                    draw_idx = np.where(encoder.classes_ == "Draw")[0][0]
                    class_weights[draw_idx] *= 2.5  # Increase weight for Draw class

                # Train with early stopping and learning rate reduction
                early_stopping = tf.keras.callbacks.EarlyStopping(
                    monitor="val_loss", patience=10, restore_best_weights=True
                )

                reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
                    monitor="val_loss", factor=0.2, patience=5, min_lr=1e-6
                )

                history = model.fit(
                    X_train_scaled,
                    y_train_cat,
                    epochs=200,
                    batch_size=32,
                    validation_split=0.2,
                    class_weight=class_weights,
                    callbacks=[early_stopping, reduce_lr],
                    verbose=1,  # Changed to 1 to see training progress
                )

                # model.fit(
                #     X_train_scaled,
                #     y_train_cat,
                #     epochs=200,
                #     batch_size=32,
                #     validation_split=0.2,
                #     class_weight=class_weights,
                #     callbacks=[early_stopping, reduce_lr],
                #     verbose=0,
                # )

                # Evaluate
                y_pred = np.argmax(model.predict(X_test_scaled), axis=1)
                accuracy = accuracy_score(y_test_encoded, y_pred)

                models[pred_type] = {
                    "model": model,
                    "accuracy": accuracy,
                    "feature_names": numeric_columns,
                    "y_test": y_test_encoded,
                    "X_test": X_test_scaled,
                    "scaler": scaler,
                    "num_classes": len(encoder.classes_),
                    "encoder": encoder,
                    "history": history.history # store training history for analysis
                }

                # model.fit(X_train_scaled, y_train_encoded)
                # y_pred = model.predict(X_test_scaled)
                # accuracy = accuracy_score(y_test_encoded, y_pred)
                #
                # models[pred_type] = {
                #     "model": model,
                #     "accuracy": accuracy,
                #     "feature_names": numeric_columns,
                #     "y_test": y_test_encoded,
                #     "X_test": X_test_scaled,
                #     "scaler": scaler,
                #     "encoder": encoder,
                # }

            # elif pred_type == "double_chance":
            #     # Use RandomForest for double_chance prediction
            #     model = RandomForestClassifier(
            #         n_estimators=100,
            #         max_depth=10,
            #         min_samples_split=10,
            #         min_samples_leaf=5,
            #         class_weight="balanced",
            #         max_features="sqrt",
            #         random_state=42,
            #         bootstrap=True,
            #         oob_score=True,
            #     )
            #
            #     model.fit(X_train_scaled, y_train_encoded)
            #     y_pred = model.predict(X_test_scaled)
            #     accuracy = accuracy_score(y_test_encoded, y_pred)
            #
            #     models[pred_type] = {
            #         "model": model,
            #         "accuracy": accuracy,
            #         "feature_names": numeric_columns,
            #         "y_test": y_test_encoded,
            #         "X_test": X_test_scaled,
            #         "scaler": scaler,
            #         "encoder": encoder,
            #     }

            else:
                # For over/under predictions, use RandomForest with adjusted parameters
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=10,
                    min_samples_leaf=5,
                    class_weight="balanced",
                    max_features="sqrt",
                    random_state=42,
                    bootstrap=True,
                    oob_score=True,
                )

                model.fit(X_train_scaled, y_train_encoded)
                y_pred = model.predict(X_test_scaled)
                accuracy = accuracy_score(y_test_encoded, y_pred)

                # Log out-of-bag score
                logger.info(f"Out-of-bag score for {pred_type}: {model.oob_score_:.4f}")

                models[pred_type] = {
                    "model": model,
                    "accuracy": accuracy,
                    "feature_names": numeric_columns,
                    "y_test": y_test_encoded,
                    "X_test": X_test_scaled,
                    "scaler": scaler,
                    "encoder": encoder,
                }

            # Log results
            y_pred_original = encoder.inverse_transform(y_pred)
            y_test_original = encoder.inverse_transform(y_test_encoded)

            logger.info(f"{pred_type.capitalize()} Model Accuracy: {accuracy:.4f}")
            logger.info("\nClassification Report:")
            logger.info(safe_classification_report(y_test_original, y_pred_original))

            # Create confusion matrix visualization
            cm = confusion_matrix(y_test_original, y_pred_original)
            plt.figure(figsize=(10, 8))
            sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")
            plt.title(f"Confusion Matrix - {pred_type}")
            plt.ylabel("True Label")
            plt.xlabel("Predicted Label")
            plt.savefig(
                os.path.join(get_image_directory(), f"confusion_matrix_{pred_type}.png")
            )
            plt.close()

            # Analyze feature importance only for non-neural network models
            if not isinstance(model, tf.keras.Sequential):
                analyze_feature_importance(
                    model,
                    X_test_scaled,
                    y_test_encoded,
                    numeric_columns,
                    pred_type,
                    label_encoders,
                )

        except Exception as e:
            logger.error(f"Error training model for {pred_type}: {str(e)}")
            logger.error(traceback.format_exc())
            continue

    return models


def analyze_feature_importance(model, X, y, feature_names, pred_type, label_encoders):
    """
    Analyze feature importance for various types of models.

    Args:
        model: Trained model.
        X (pd.DataFrame): Feature DataFrame for testing.
        y (np.array): Encoded target variable for testing.
        feature_names (list): List of feature names.
        pred_type (str): Prediction type (e.g., 'three_way', 'over_under_2_5').
        label_encoders (dict): Dictionary of label encoders for each prediction type.

    Returns:
        pd.DataFrame: DataFrame containing feature importances.
    """
    try:
        if isinstance(model, tf.keras.Sequential):
            # Skip feature importance for neural networks
            return None
        else:
            # For tree-based models, use built-in feature importance
            importance = model.feature_importances_

        # Create and sort feature importance DataFrame
        feature_importance = pd.DataFrame(
            {"feature": feature_names, "importance": importance}
        ).sort_values("importance", ascending=False)

        # Log results
        logger.info("Feature Importances:")
        logger.info(feature_importance.head(10))

        for feat, imp in feature_importance.values:
            logger.info(f"{feat}: {imp:.4f}")

        # Create visualization
        plt.figure(figsize=(12, 6))
        sns.barplot(x="importance", y="feature", data=feature_importance.head(20))
        plt.title(f"Feature Importance for {pred_type}")
        plt.tight_layout()
        plt.savefig(
            os.path.join(get_image_directory(), f"feature_importance_{pred_type}.png")
        )
        plt.close()

        # Analyze zero-importance features
        zero_importance = feature_importance[feature_importance["importance"] == 0]
        if not zero_importance.empty:
            logger.warning(
                f"Features with zero importance: {zero_importance['feature'].tolist()}"
            )
            logger.warning(
                "Consider removing these features or investigating why they have no importance."
            )

        return feature_importance

    except Exception as e:
        logger.error(f"Error in feature importance analysis: {str(e)}")
        return None


def perform_cross_validation(model, X, y, n_splits=5, label_encoders=None):
    """
    Perform time series cross-validation.

    Args:
        model: Trained model.
        X (pd.DataFrame): Feature DataFrame.
        y (pd.Series): Target variable.
        n_splits (int): Number of splits for cross-validation.
        label_encoders (dict): Dictionary of label encoders for each prediction type.

    Returns:
        None
    """
    logger.info(f"Performing {n_splits}-fold time series cross-validation")

    # Remove string columns
    string_columns = [
        "Team",
        "Home Team",
        "Away Team",
        "correct_score",
        "form_data_valid_str",
    ]
    numeric_columns = [col for col in X.columns if col not in string_columns]
    X_numeric = X[numeric_columns]

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_numeric)
    X_scaled = pd.DataFrame(X_scaled, columns=numeric_columns)

    tscv = TimeSeriesSplit(n_splits=n_splits)
    scores = []

    if isinstance(model, tf.keras.Sequential):
        # Get number of classes from the model's last layer
        num_classes = model.layers[-1].get_config()["units"]

        # Handle neural network models
        encoder = LabelEncoder()
        y_encoded = encoder.fit_transform(y)
        y_cat = tf.keras.utils.to_categorical(y_encoded, num_classes=num_classes)

        for train_index, val_index in tscv.split(X_scaled):
            X_train, X_val = X_scaled.iloc[train_index], X_scaled.iloc[val_index]
            y_train, y_val = y_cat[train_index], y_cat[val_index]

            # Clone model for each fold
            fold_model = tf.keras.models.clone_model(model)
            fold_model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=0.0005),
                loss="categorical_crossentropy",
                metrics=["accuracy"],
            )

            # Calculate class weights for this fold
            y_train_classes = np.argmax(y_train, axis=1)
            class_counts = np.bincount(y_train_classes)
            total_samples = len(y_train_classes)
            class_weights = {
                i: total_samples / (len(class_counts) * count)
                for i, count in enumerate(class_counts)
            }

            # Adjust weights to give more importance to Draw class if it exists
            if "Draw" in encoder.classes_:
                draw_idx = np.where(encoder.classes_ == "Draw")[0][0]
                class_weights[draw_idx] *= 1.5  # Increase weight for Draw class

            # Train with early stopping
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor="val_loss", patience=10, restore_best_weights=True
            )

            reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
                monitor="val_loss", factor=0.2, patience=5, min_lr=1e-6
            )

            fold_model.fit(
                X_train,
                y_train,
                epochs=100,
                batch_size=32,
                validation_data=(X_val, y_val),
                class_weight=class_weights,
                callbacks=[early_stopping, reduce_lr],
                verbose=0,
            )

            # Evaluate
            y_pred = np.argmax(fold_model.predict(X_val), axis=1)
            score = accuracy_score(np.argmax(y_val, axis=1), y_pred)
            scores.append(score)
    else:
        # Handle other models
        for train_index, val_index in tscv.split(X_scaled):
            X_train, X_val = X_scaled.iloc[train_index], X_scaled.iloc[val_index]
            y_train, y_val = y.iloc[train_index], y.iloc[val_index]

            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            score = accuracy_score(y_val, y_pred)
            scores.append(score)

    logger.info(f"Cross-validation scores: {scores}")
    logger.info(f"Mean CV score: {np.mean(scores):.4f} (+/- {np.std(scores) * 2:.4f})")
