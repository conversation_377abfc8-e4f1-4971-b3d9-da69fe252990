"""
This is the main entry point for the betting project.
It orchestrates the entire process, including data loading, feature engineering, model training, and prediction.
"""

import logging
import pandas as pd
import numpy as np
import os

# Import from modularized packages
from data_loading import (
    load_data,
    get_available_leagues,
    validate_and_convert_data
)
from feature_engineering import (
    prepare_features,
    engineer_features,
    normalize_features
)
from model_training import (
    train_model,
    perform_cross_validation,
    analyze_feature_importance,
    analyze_neural_network_importance
)
from analysis import (
    perform_model_analysis,
    analyze_prediction_results,
    analyze_feature_relationships
)
from prediction import (
    predict_match,
    predict_correct_scores,
    analyze_prediction_confidence,
    assess_prediction_risk,
    generate_prediction_summary,
    save_predictions_to_excel
)
from utils import setup_logging, add_parent_dir_to_path, standardize_team_name

logger = logging.getLogger(__name__)

# Import league configs
try:
    from src.scrapers.config import LEAGUE_CONFIGS
except ImportError:
    logger.warning(
        "Failed to import LEAGUE_CONFIGS from src.scrapers.config. Using an empty dict."
    )
    LEAGUE_CONFIGS = {}

def setup_output_directories():
    """Create necessary output directories."""
    directories = [
        'data/processed',
        'data/processed/img',
        'analysis_output',
        'models'
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def validate_league_data(data, league_name):
    """Validate loaded league data."""
    if data is None:
        logger.warning(f"Skipping {league_name} due to incomplete data.")
        return False

    results, team_stats, league_stats, h2h_stats, league_table = data

    if results.empty or team_stats.empty or league_stats.empty:
        logger.error(f"One or more required dataframes are empty for {league_name}")
        return False

    logger.debug(f"Results columns: {results.columns.tolist()}")
    logger.debug(f"Team stats columns: {team_stats.columns.tolist()}")
    logger.debug(f"League stats columns: {league_stats.columns.tolist()}")
    
    return True

def prepare_team_mappings(league_config, team_stats):
    """Prepare and validate team name mappings."""
    if "TEAM_NAME_MAPPING" not in league_config:
        logger.warning("TEAM_NAME_MAPPING not found. Using empty mapping.")
        league_config["TEAM_NAME_MAPPING"] = {}

    # Create reverse mapping (from full name to short name)
    reverse_mapping = {v: k for k, v in league_config["TEAM_NAME_MAPPING"].items()}

    # Update mapping with any new teams
    for team in team_stats["Team"].unique():
        if team not in league_config["TEAM_NAME_MAPPING"]:
            league_config["TEAM_NAME_MAPPING"][team] = team
            reverse_mapping[team] = team

    league_config["REVERSE_TEAM_NAME_MAPPING"] = reverse_mapping
    return league_config

def get_column_mappings():
    """Get column mappings for different data types."""
    return {
        "team_stats": {
            "points_per_game": "points_per_game",
            "goals_scored_per_match_home": "goals_scored_per_match_home",
            "goals_scored_per_match_away": "goals_scored_per_match_away",
            "goals_conceded_per_match_home": "goals_conceded_per_match_home",
            "goals_conceded_per_match_away": "goals_conceded_per_match_away",
            "total_home_wins": "total_home_wins",
            "total_home_played": "total_home_played",
            "total_away_wins": "total_away_wins",
            "total_away_played": "total_away_played",
            "total_home_draws": "total_home_draws",
            "total_away_draws": "total_away_draws",
            "ppg_last_8": "ppg_last_8",
            "avg_goals_scored_last_8": "avg_goals_scored_last_8",
            "avg_goals_conceded_last_8": "avg_goals_conceded_last_8",
        },
        "h2h_stats": {
            "team_a_win_percentage": "team_a_win_percentage",
            "team_b_win_percentage": "team_b_win_percentage",
            "draw_percentage": "draw_percentage",
            "total_matches": "total_matches",
            "team_a_goals": "team_a_goals",
            "team_b_goals": "team_b_goals",
            "btts_percentage": "btts_percentage",
        }
    }

def prepare_prediction_data(prepared_data):
    """Prepare X and y data for predictions."""
    columns_to_drop = [
        "result", "three_way", "double_chance",
        "over_under_1_5", "over_under_2_5", "over_under_3_5",
        "btts", "home_goals", "away_goals", "total_goals",
        "three_way_encoded", "double_chance_encoded",
        "over_under_1_5_encoded", "over_under_2_5_encoded",
        "over_under_3_5_encoded", "btts_encoded",
        "form_data_valid_str",
    ]

    X = prepared_data.drop(
        [col for col in columns_to_drop if col in prepared_data.columns],
        axis=1
    )

    y_dict = {
        "three_way": prepared_data["three_way"],
        "double_chance": prepared_data["double_chance"],
        "over_under_1_5": prepared_data["over_under_1_5"],
        "over_under_2_5": prepared_data["over_under_2_5"],
        "over_under_3_5": prepared_data["over_under_3_5"],
        "btts": prepared_data["btts"],
    }

    return X, y_dict

def get_matches_to_predict(league_config, results):
    """Get list of matches to predict."""
    matches = []
    head_to_head_urls = league_config.get("HEAD_TO_HEAD_URLS", {})
    logger.debug(f"Head to head URLs: {head_to_head_urls}")

    if head_to_head_urls:
        try:
            # Extract team names from URLs and convert to short names using reverse mapping
            matches = []
            reverse_mapping = league_config.get("REVERSE_TEAM_NAME_MAPPING", {})
            for url in head_to_head_urls.keys():
                home_team, away_team = url.split(" vs ")
                # Convert full names to short names using reverse mapping
                short_home_team = reverse_mapping.get(home_team, home_team)
                short_away_team = reverse_mapping.get(away_team, away_team)
                matches.append((short_home_team, short_away_team))
        except Exception as e:
            logger.error(f"Error processing HEAD_TO_HEAD_URLS: {str(e)}")
            matches = []

    if not matches:
        logger.info("No HEAD_TO_HEAD_URLS found. Using all matches.")
        matches = [
            (row["Home Team"], row["Away Team"])
            for _, row in results.iterrows()
        ]

    return matches

def main():
    """
    Main function to run the prediction model with extended prediction types for all leagues.
    Includes enhanced analysis, confidence assessment, and risk evaluation.
    """
    # Setup
    setup_logging()
    add_parent_dir_to_path()
    setup_output_directories()

    # Get available leagues
    available_leagues = get_available_leagues()
    if not available_leagues:
        logger.error("No league data found in the raw directory. Exiting.")
        return

    logger.info(f"Found data for the following leagues: {', '.join(available_leagues)}")

    # Process each league
    for league_name in available_leagues:
        if league_name not in LEAGUE_CONFIGS:
            logger.warning(f"Configuration not found for {league_name}. Skipping.")
            continue

        league_config = LEAGUE_CONFIGS[league_name]
        if league_config is None:
            logger.error(f"League configuration not found for {league_name}. Skipping.")
            continue

        try:
            logger.info(f"Starting prediction process for league: {league_name}")
            
            # Load and validate data
            data, league_config = load_data(league_name, league_config)
            if not validate_league_data(data, league_name):
                continue

            results, team_stats, league_stats, h2h_stats, league_table = data

            # Prepare team mappings
            league_config = prepare_team_mappings(league_config, team_stats)

            # Get column mappings
            mappings = get_column_mappings()
            combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}

            # Prepare features
            prepared_data = prepare_features(
                results, team_stats, league_stats, h2h_stats, league_table,
                combined_mapping, league_config["TEAM_NAME_MAPPING"]
            )

            if prepared_data is None:
                logger.warning(f"Skipping {league_name} due to feature preparation failure.")
                continue

            # Prepare X and y data
            try:
                X, y_dict = prepare_prediction_data(prepared_data)
                label_encoders = prepared_data.attrs.get("label_encoders", {})
            except Exception as e:
                logger.error(f"Error preparing X and y data: {str(e)}")
                continue

            # Validate data
            nan_columns = X.columns[X.isna().any()].tolist()
            if nan_columns:
                logger.error("NaN values found in the following columns:")
                for col in nan_columns:
                    logger.error(f"  {col}: {X[col].isna().sum()} NaN values")
                continue

            # Train models
            try:
                models = train_model(X, y_dict, label_encoders)
                if not models:
                    logger.error(f"No models trained for {league_name}. Skipping predictions.")
                    continue
            except Exception as e:
                logger.error(f"Error training models: {str(e)}")
                continue

            # Analyze models
            for pred_type, model_info in models.items():
                logger.info(f"\nAnalyzing {pred_type} model...")
                perform_model_analysis(
                    model_info["model"],
                    X,
                    y_dict[pred_type],
                    model_info["feature_names"],
                    pred_type
                )

            # Get matches to predict
            matches = get_matches_to_predict(league_config, results)
            
            # Make predictions
            match_predictions = {}
            avg_goals_per_match = float(
                league_stats[league_stats["Stat"] == "avg_goals_per_match"]["Value"].values[0]
            )

            for home_team, away_team in matches:
                logger.info(f"\nPredicting {home_team} vs {away_team}...")
                
                if home_team not in team_stats["Team"].values or away_team not in team_stats["Team"].values:
                    logger.error(f"Team statistics missing for {home_team} or {away_team}")
                    continue

                # Make a single prediction with all models
                try:
                    pred_results, error_message, correct_scores = predict_match(
                        models,  # Pass all models at once
                        home_team,
                        away_team,
                        team_stats,
                        league_stats,
                        h2h_stats,
                        league_table,
                        combined_mapping,
                        models["three_way"]["feature_names"],  # Use feature names from any model
                        avg_goals_per_match,
                        label_encoders=label_encoders,
                        bias_correction=0.05,
                        log_features=True
                    )

                    if error_message:
                        logger.error(f"Prediction error: {error_message}")
                        continue

                    # Extract predictions from the nested structure
                    predictions = pred_results.get("main_predictions", {})
                    
                    # Analyze confidence and risk
                    confidence_analysis = analyze_prediction_confidence(predictions)
                    risk_assessment = assess_prediction_risk(predictions, confidence_analysis)
                    
                    # Generate match summary
                    match_summary = generate_prediction_summary(
                        predictions,
                        correct_scores,
                        confidence_analysis,
                        risk_assessment=risk_assessment
                    )
                    
                    match_predictions[f"{home_team} vs {away_team}"] = match_summary

                except Exception as e:
                    logger.error(f"Error making prediction: {str(e)}")
                    continue

            # Save predictions
            if match_predictions:
                try:
                    save_predictions_to_excel(match_predictions, league_name)
                except Exception as e:
                    logger.error(f"Error saving predictions: {str(e)}")
            else:
                logger.error(f"No predictions generated for {league_name}")

        except Exception as e:
            logger.error(f"Error processing {league_name}: {str(e)}")
            continue

    logger.info("Prediction process completed for all leagues.")

if __name__ == "__main__":
    main()
