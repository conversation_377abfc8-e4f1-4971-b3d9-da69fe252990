"""
🏆 BETTING PROJECT - <PERSON>IN PREDICTION SYSTEM
============================================

This is the main entry point for the betting project with hybrid input support:
1. Command Line: python src/main.py --league EPL --home Liverpool --away Arsenal
2. Interactive Mode: python src/main.py --interactive
3. Batch Mode: python src/main.py --config predictions.yaml
4. Default Mode: python src/main.py (shows available options)

Features:
- All prediction types (three-way, over/under 1.5/2.5/3.5, BTTS, double chance)
- Expected goals with realistic values
- Complete output files (markdown + Excel + visualizations)
- League-specific logging and data storage
- Confidence analysis and risk assessment
"""

import logging
import pandas as pd
import numpy as np
import os
import sys
import argparse
import yaml
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from sklearn.preprocessing import LabelEncoder

# Import from modularized packages
from data_loading import load_data, get_available_leagues
from feature_engineering import prepare_features
from model_training import train_model
from prediction import (
    predict_match,
    analyze_prediction_confidence,
    assess_prediction_risk,
    generate_prediction_summary
)
from prediction.markdown_output import save_predictions_to_markdown
from prediction.excel_output import save_predictions_to_excel
from utils import setup_logging, add_parent_dir_to_path, get_image_directory
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)

# Import league configs
try:
    from scrapers.config import LEAGUE_CONFIGS
except ImportError:
    logger.warning("Failed to import LEAGUE_CONFIGS. Using empty dict.")
    LEAGUE_CONFIGS = {}

def setup_league_logging(league_name: str, match_name: str) -> str:
    """Setup logging for a specific league and match."""
    # Create league-specific log directory
    log_dir = f"data/processed/{league_name}/logs"
    os.makedirs(log_dir, exist_ok=True)

    # Create timestamped log file for this match
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"{match_name.replace(' vs ', '_vs_')}_{timestamp}.log"
    log_path = os.path.join(log_dir, log_filename)

    # Add file handler for this specific match
    file_handler = logging.FileHandler(log_path)
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # Add to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)

    logger.info(f"Match-specific logging setup: {log_path}")
    return log_path

def setup_output_directories(league_name: str = None):
    """Create necessary output directories."""
    base_directories = [
        'data/processed',
        'analysis_output',
        'models'
    ]

    for directory in base_directories:
        os.makedirs(directory, exist_ok=True)

    # Create league-specific directories if specified
    if league_name:
        league_directories = [
            f'data/processed/{league_name}',
            f'data/processed/{league_name}/logs',
            f'data/processed/{league_name}/img'
        ]
        for directory in league_directories:
            os.makedirs(directory, exist_ok=True)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="🏆 Betting Project - Football Match Prediction System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Single match prediction
  python src/main.py --league ENGLAND_PREMIER_LEAGUE --home Liverpool --away Arsenal

  # Interactive mode
  python src/main.py --interactive

  # Batch predictions from config file
  python src/main.py --config predictions.yaml

  # Show available options
  python src/main.py
        """
    )

    parser.add_argument('--league', '-l', type=str, help='League name (e.g., ENGLAND_PREMIER_LEAGUE)')
    parser.add_argument('--home', type=str, help='Home team name')
    parser.add_argument('--away', type=str, help='Away team name')
    parser.add_argument('--interactive', '-i', action='store_true', help='Interactive mode')
    parser.add_argument('--config', '-c', type=str, help='Config file for batch predictions')
    parser.add_argument('--list-leagues', action='store_true', help='List available leagues')
    parser.add_argument('--list-teams', type=str, help='List teams in specified league')

    return parser.parse_args()

def get_available_leagues_display():
    """Get available leagues with display formatting."""
    leagues = get_available_leagues()
    if not leagues:
        return []

    # Create display mapping for common leagues
    display_mapping = {
        'ENGLAND_PREMIER_LEAGUE': 'English Premier League (EPL)',
        'SPAIN_LA_LIGA': 'Spanish La Liga',
        'GERMANY_BUNDESLIGA': 'German Bundesliga',
        'ITALY_SERIE_A': 'Italian Serie A',
        'FRANCE_LIGUE_1': 'French Ligue 1',
    }

    return [(league, display_mapping.get(league, league)) for league in leagues]

def validate_league_data(data, league_name):
    """Validate loaded league data."""
    if data is None:
        logger.warning(f"Skipping {league_name} due to incomplete data.")
        return False

    results, team_stats, league_stats, h2h_stats, league_table = data

    if results.empty or team_stats.empty or league_stats.empty:
        logger.error(f"One or more required dataframes are empty for {league_name}")
        return False

    logger.debug(f"Results columns: {results.columns.tolist()}")
    logger.debug(f"Team stats columns: {team_stats.columns.tolist()}")
    logger.debug(f"League stats columns: {league_stats.columns.tolist()}")

    return True

def interactive_mode():
    """Interactive mode for selecting league and teams."""
    print("\n🏆 BETTING PROJECT - INTERACTIVE MODE")
    print("=" * 50)

    # Select league
    leagues = get_available_leagues_display()
    if not leagues:
        print("❌ No league data found!")
        return None

    print("\n📊 Available Leagues:")
    for i, (league_code, league_display) in enumerate(leagues, 1):
        print(f"  {i}. {league_display}")

    while True:
        try:
            choice = input(f"\nSelect league (1-{len(leagues)}): ").strip()
            if choice.lower() in ['q', 'quit', 'exit']:
                return None

            league_idx = int(choice) - 1
            if 0 <= league_idx < len(leagues):
                selected_league = leagues[league_idx][0]
                break
            else:
                print(f"❌ Please enter a number between 1 and {len(leagues)}")
        except ValueError:
            print("❌ Please enter a valid number")

    # Load league data to get teams
    try:
        league_config = LEAGUE_CONFIGS.get(selected_league, {})
        data_tuple, _ = load_data(selected_league, league_config)
        if not validate_league_data(data_tuple, selected_league):
            print(f"❌ Failed to load data for {selected_league}")
            return None

        results, team_stats, league_stats, h2h_stats, league_table = data_tuple
        teams = sorted(team_stats["Team"].unique())

    except Exception as e:
        print(f"❌ Error loading league data: {e}")
        return None

    # Select home team
    print(f"\n🏠 Teams in {leagues[league_idx][1]}:")
    for i, team in enumerate(teams, 1):
        print(f"  {i:2d}. {team}")

    while True:
        try:
            choice = input(f"\nSelect HOME team (1-{len(teams)}): ").strip()
            if choice.lower() in ['q', 'quit', 'exit']:
                return None

            home_idx = int(choice) - 1
            if 0 <= home_idx < len(teams):
                home_team = teams[home_idx]
                break
            else:
                print(f"❌ Please enter a number between 1 and {len(teams)}")
        except ValueError:
            print("❌ Please enter a valid number")

    # Select away team (exclude home team)
    away_teams = [team for team in teams if team != home_team]
    print(f"\n✈️ Available AWAY teams:")
    for i, team in enumerate(away_teams, 1):
        print(f"  {i:2d}. {team}")

    while True:
        try:
            choice = input(f"\nSelect AWAY team (1-{len(away_teams)}): ").strip()
            if choice.lower() in ['q', 'quit', 'exit']:
                return None

            away_idx = int(choice) - 1
            if 0 <= away_idx < len(away_teams):
                away_team = away_teams[away_idx]
                break
            else:
                print(f"❌ Please enter a number between 1 and {len(away_teams)}")
        except ValueError:
            print("❌ Please enter a valid number")

    # Ask for multiple matches
    matches = [(home_team, away_team)]

    while True:
        add_more = input("\n➕ Add another match? (y/n): ").strip().lower()
        if add_more in ['n', 'no']:
            break
        elif add_more in ['y', 'yes']:
            # Quick selection for additional matches
            print(f"\n🏠 Select another HOME team:")
            for i, team in enumerate(teams, 1):
                print(f"  {i:2d}. {team}")

            try:
                choice = int(input(f"HOME team (1-{len(teams)}): ")) - 1
                if 0 <= choice < len(teams):
                    home_team = teams[choice]

                    away_teams = [team for team in teams if team != home_team]
                    print(f"\n✈️ Select AWAY team:")
                    for i, team in enumerate(away_teams, 1):
                        print(f"  {i:2d}. {team}")

                    choice = int(input(f"AWAY team (1-{len(away_teams)}): ")) - 1
                    if 0 <= choice < len(away_teams):
                        away_team = away_teams[choice]
                        matches.append((home_team, away_team))
                        print(f"✅ Added: {home_team} vs {away_team}")
            except (ValueError, IndexError):
                print("❌ Invalid selection, skipping...")
        else:
            print("❌ Please enter 'y' or 'n'")

    return {
        'league': selected_league,
        'matches': matches
    }

def load_config_file(config_path: str) -> Optional[Dict]:
    """Load configuration file for batch predictions."""
    try:
        with open(config_path, 'r') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                config = yaml.safe_load(f)
            else:
                print(f"❌ Unsupported config file format: {config_path}")
                return None

        # Validate config structure
        if 'matches' not in config:
            print("❌ Config file must contain 'matches' section")
            return None

        return config
    except Exception as e:
        print(f"❌ Error loading config file: {e}")
        return None

def show_available_options():
    """Show available leagues and usage examples."""
    print("\n🏆 BETTING PROJECT - FOOTBALL MATCH PREDICTION SYSTEM")
    print("=" * 60)

    leagues = get_available_leagues_display()
    if leagues:
        print("\n📊 Available Leagues:")
        for league_code, league_display in leagues:
            print(f"  • {league_display} ({league_code})")
    else:
        print("\n❌ No league data found!")
        return

    print("\n🚀 Usage Options:")
    print("  1. Single Match:")
    print("     python src/main.py --league ENGLAND_PREMIER_LEAGUE --home Liverpool --away Arsenal")
    print("\n  2. Interactive Mode:")
    print("     python src/main.py --interactive")
    print("\n  3. Batch Predictions:")
    print("     python src/main.py --config predictions.yaml")
    print("\n  4. List Teams:")
    print("     python src/main.py --list-teams ENGLAND_PREMIER_LEAGUE")
    print("\n  5. List Leagues:")
    print("     python src/main.py --list-leagues")

def get_column_mappings():
    """Get column mappings for different data types."""
    return {
        "team_stats": {
            "points_per_game": "points_per_game",
            "goals_scored_per_match_home": "goals_scored_per_match_home",
            "goals_scored_per_match_away": "goals_scored_per_match_away",
            "goals_conceded_per_match_home": "goals_conceded_per_match_home",
            "goals_conceded_per_match_away": "goals_conceded_per_match_away",
            "total_home_wins": "total_home_wins",
            "total_home_played": "total_home_played",
            "total_away_wins": "total_away_wins",
            "total_away_played": "total_away_played",
            "total_home_draws": "total_home_draws",
            "total_away_draws": "total_away_draws",
            "ppg_last_8": "ppg_last_8",
            "avg_goals_scored_last_8": "avg_goals_scored_last_8",
            "avg_goals_conceded_last_8": "avg_goals_conceded_last_8",
        },
        "h2h_stats": {
            "team_a_win_percentage": "team_a_win_percentage",
            "team_b_win_percentage": "team_b_win_percentage",
            "draw_percentage": "draw_percentage",
            "total_matches": "total_matches",
            "team_a_goals": "team_a_goals",
            "team_b_goals": "team_b_goals",
            "btts_percentage": "btts_percentage",
        }
    }

def prepare_prediction_data(prepared_data):
    """Prepare X and y data for predictions."""
    columns_to_drop = [
        "result", "three_way", "double_chance",
        "over_under_1_5", "over_under_2_5", "over_under_3_5",
        "btts", "home_goals", "away_goals", "total_goals",
        "three_way_encoded", "double_chance_encoded",
        "over_under_1_5_encoded", "over_under_2_5_encoded",
        "over_under_3_5_encoded", "btts_encoded",
        "form_data_valid_str",
    ]

    X = prepared_data.drop(
        [col for col in columns_to_drop if col in prepared_data.columns],
        axis=1
    )

    # Build y_dict only with columns that exist
    y_dict = {}
    target_columns = {
        "three_way": "three_way",
        "double_chance": "double_chance",
        "over_under_1_5": "over_under_1_5",
        "over_under_2_5": "over_under_2_5",
        "over_under_3_5": "over_under_3_5",
        "btts": "btts",
    }

    for key, col_name in target_columns.items():
        if col_name in prepared_data.columns:
            y_dict[key] = prepared_data[col_name]
        else:
            logger.warning(f"⚠️ Target column '{col_name}' not found in prepared data")

    if not y_dict:
        logger.error("❌ No target columns found in prepared data")
        logger.error(f"Available columns: {list(prepared_data.columns)}")
        return None, None

    logger.info(f"✅ Prepared prediction data with {len(y_dict)} target variables: {list(y_dict.keys())}")
    return X, y_dict

def generate_visualizations(match_predictions: Dict, league_name: str):
    """Generate prediction visualizations."""
    try:
        img_dir = get_image_directory(league_name)

        # Collect all predictions for visualization
        all_predictions = {}
        prediction_types = ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']

        for pred_type in prediction_types:
            all_predictions[pred_type] = []

        # Extract predictions from all matches
        for match_name, match_data in match_predictions.items():
            main_pred = match_data.get('main_predictions', {})

            for pred_type in prediction_types:
                pred_data = main_pred.get(pred_type, {})
                probs = pred_data.get('probabilities', {})
                if probs:
                    all_predictions[pred_type].append(probs)

        # Generate distribution charts
        for pred_type, predictions in all_predictions.items():
            if not predictions:
                continue

            plt.figure(figsize=(10, 6))

            if pred_type == 'three_way':
                home_probs = [p.get('Home', 0) for p in predictions]
                draw_probs = [p.get('Draw', 0) for p in predictions]
                away_probs = [p.get('Away', 0) for p in predictions]

                plt.hist([home_probs, draw_probs, away_probs],
                        label=['Home', 'Draw', 'Away'], alpha=0.7, bins=10)
                plt.xlabel('Probability')
                plt.ylabel('Frequency')
                plt.title('Three-Way Prediction Distribution')
                plt.legend()

            elif pred_type.startswith('over_under'):
                threshold = pred_type.split('_')[-1].replace('_', '.')
                over_probs = [p.get(f'Over {threshold}', 0) for p in predictions]
                under_probs = [p.get(f'Under {threshold}', 0) for p in predictions]

                plt.hist([over_probs, under_probs],
                        label=[f'Over {threshold}', f'Under {threshold}'], alpha=0.7, bins=10)
                plt.xlabel('Probability')
                plt.ylabel('Frequency')
                plt.title(f'Over/Under {threshold} Prediction Distribution')
                plt.legend()

            elif pred_type == 'btts':
                yes_probs = [p.get('Yes', 0) for p in predictions]
                no_probs = [p.get('No', 0) for p in predictions]

                plt.hist([yes_probs, no_probs],
                        label=['BTTS Yes', 'BTTS No'], alpha=0.7, bins=10)
                plt.xlabel('Probability')
                plt.ylabel('Frequency')
                plt.title('Both Teams to Score Prediction Distribution')
                plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(img_dir, f'prediction_distribution_{pred_type}.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

        # Generate confidence heatmap
        if match_predictions:
            matches = list(match_predictions.keys())
            confidence_data = []

            for match_name, match_data in match_predictions.items():
                confidence = match_data.get('confidence_analysis', {})
                row = [
                    confidence.get('three_way', {}).get('confidence_level', 0),
                    confidence.get('over_under_1_5', {}).get('confidence_level', 0),
                    confidence.get('over_under_2_5', {}).get('confidence_level', 0),
                    confidence.get('over_under_3_5', {}).get('confidence_level', 0),
                    confidence.get('btts', {}).get('confidence_level', 0)
                ]
                confidence_data.append(row)

            if confidence_data:
                plt.figure(figsize=(12, 8))
                sns.heatmap(confidence_data,
                           xticklabels=['Three-Way', 'O/U 1.5', 'O/U 2.5', 'O/U 3.5', 'BTTS'],
                           yticklabels=[m.replace(' vs ', '\nvs\n') for m in matches],
                           annot=True, fmt='.1f', cmap='RdYlGn')
                plt.title('Prediction Confidence Heatmap')
                plt.tight_layout()
                plt.savefig(os.path.join(img_dir, 'prediction_confidence_heatmap.png'),
                           dpi=300, bbox_inches='tight')
                plt.close()

        logger.info(f"✅ Generated visualizations in {img_dir}")

    except Exception as e:
        logger.error(f"❌ Error generating visualizations: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

def predict_matches(league_name: str, matches: List[Tuple[str, str]]) -> bool:
    """Predict matches for a specific league."""
    try:
        logger.info(f"🚀 Starting predictions for {league_name}")
        logger.info(f"📊 Matches to predict: {len(matches)}")

        # Setup league-specific directories
        setup_output_directories(league_name)

        # Get league config
        league_config = LEAGUE_CONFIGS.get(league_name, {})
        if not league_config:
            logger.warning(f"⚠️ No configuration found for {league_name}, using defaults")
            league_config = {}

        # Load league data
        data_tuple, _ = load_data(league_name, league_config)
        if not validate_league_data(data_tuple, league_name):
            logger.error(f"❌ Failed to load data for {league_name}")
            return False

        results, team_stats, league_stats, h2h_stats, league_table = data_tuple

        # Get column mappings
        mappings = get_column_mappings()
        combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}

        # Prepare features
        prepared_data = prepare_features(
            results, team_stats, league_stats, h2h_stats, league_table,
            combined_mapping, league_config.get('TEAM_NAME_MAPPING', {})
        )

        if prepared_data is None:
            logger.error(f"❌ Feature preparation failed for {league_name}")
            return False

        # Prepare X and y data
        X, y_dict = prepare_prediction_data(prepared_data)
        if X is None or y_dict is None:
            logger.error(f"❌ Failed to prepare prediction data for {league_name}")
            return False

        label_encoders = prepared_data.attrs.get("label_encoders", {})

        # Validate data
        nan_columns = X.columns[X.isna().any()].tolist()
        if nan_columns:
            logger.error("❌ NaN values found in the following columns:")
            for col in nan_columns:
                logger.error(f"  {col}: {X[col].isna().sum()} NaN values")
            return False

        # Train models
        logger.info("🤖 Training prediction models...")
        models = train_model(X, y_dict, label_encoders)
        if not models:
            logger.error(f"❌ No models trained for {league_name}")
            return False

        logger.info("✅ Models trained successfully")

        # Make predictions
        match_predictions = {}
        avg_goals_per_match = float(
            league_stats[league_stats["Stat"] == "avg_goals_per_match"]["Value"].values[0]
        )

        for home_team, away_team in matches:
            match_name = f"{home_team} vs {away_team}"
            logger.info(f"\n🔮 Predicting: {match_name}")

            # Setup match-specific logging
            log_path = setup_league_logging(league_name, match_name)

            # Validate teams exist
            if home_team not in team_stats["Team"].values:
                logger.error(f"❌ Home team '{home_team}' not found in team statistics")
                continue
            if away_team not in team_stats["Team"].values:
                logger.error(f"❌ Away team '{away_team}' not found in team statistics")
                continue

            try:
                # Make prediction
                pred_results, error_message, correct_scores = predict_match(
                    models,
                    home_team,
                    away_team,
                    team_stats,
                    league_stats,
                    h2h_stats,
                    league_table,
                    combined_mapping,
                    models["three_way"]["feature_names"],
                    avg_goals_per_match,
                    label_encoders=label_encoders,
                    bias_correction=0.05,
                    log_features=True
                )

                if error_message:
                    logger.error(f"❌ Prediction error: {error_message}")
                    continue

                # Extract predictions
                predictions = pred_results.get("main_predictions", {})

                # Analyze confidence and risk
                confidence_analysis = analyze_prediction_confidence(predictions)
                risk_assessment = assess_prediction_risk(predictions, confidence_analysis)

                # Generate match summary
                match_summary = generate_prediction_summary(
                    predictions,
                    correct_scores,
                    confidence_analysis,
                    risk_assessment=risk_assessment
                )

                match_predictions[match_name] = match_summary
                logger.info(f"✅ Prediction completed for {match_name}")

            except Exception as e:
                logger.error(f"❌ Error predicting {match_name}: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                continue

        # Save predictions if any were generated
        if match_predictions:
            logger.info(f"\n💾 Saving predictions for {len(match_predictions)} matches...")

            try:
                # Save to markdown
                save_predictions_to_markdown(match_predictions, league_name)
                logger.info("✅ Markdown output saved")

                # Save to Excel
                save_predictions_to_excel(match_predictions, league_name)
                logger.info("✅ Excel output saved")

                # Generate visualizations
                generate_visualizations(match_predictions, league_name)
                logger.info("✅ Visualizations generated")

                logger.info(f"🎉 All predictions completed successfully for {league_name}!")
                return True

            except Exception as e:
                logger.error(f"❌ Error saving predictions: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return False
        else:
            logger.error(f"❌ No predictions generated for {league_name}")
            return False

    except Exception as e:
        logger.error(f"❌ Error in predict_matches: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """
    🏆 Main function with hybrid input support:
    1. Command Line: --league EPL --home Liverpool --away Arsenal
    2. Interactive Mode: --interactive
    3. Batch Mode: --config predictions.yaml
    4. Default Mode: Shows available options
    """
    # Setup
    setup_logging()
    add_parent_dir_to_path()
    setup_output_directories()

    # Parse arguments
    args = parse_arguments()

    # Handle list commands
    if args.list_leagues:
        leagues = get_available_leagues_display()
        print("\n📊 Available Leagues:")
        for league_code, league_display in leagues:
            print(f"  • {league_code} ({league_display})")
        return

    if args.list_teams:
        try:
            league_config = LEAGUE_CONFIGS.get(args.list_teams, {})
            data_tuple, _ = load_data(args.list_teams, league_config)
            if validate_league_data(data_tuple, args.list_teams):
                results, team_stats, league_stats, h2h_stats, league_table = data_tuple
                teams = sorted(team_stats["Team"].unique())
                print(f"\n🏠 Teams in {args.list_teams}:")
                for i, team in enumerate(teams, 1):
                    print(f"  {i:2d}. {team}")
            else:
                print(f"❌ Failed to load data for {args.list_teams}")
        except Exception as e:
            print(f"❌ Error loading teams: {e}")
        return

    # Determine input mode and get matches
    matches_to_predict = []

    if args.league and args.home and args.away:
        # Command line mode
        print(f"\n🎯 COMMAND LINE MODE")
        print(f"League: {args.league}")
        print(f"Match: {args.home} vs {args.away}")
        matches_to_predict = [{
            'league': args.league,
            'matches': [(args.home, args.away)]
        }]

    elif args.interactive:
        # Interactive mode
        print(f"\n🎮 INTERACTIVE MODE")
        result = interactive_mode()
        if result:
            matches_to_predict = [result]
        else:
            print("❌ Interactive mode cancelled")
            return

    elif args.config:
        # Config file mode
        print(f"\n📁 BATCH MODE")
        config = load_config_file(args.config)
        if not config:
            return

        # Group matches by league
        league_matches = {}
        for match in config['matches']:
            league = match['league']
            if league not in league_matches:
                league_matches[league] = []
            league_matches[league].append((match['home'], match['away']))

        matches_to_predict = [
            {'league': league, 'matches': matches}
            for league, matches in league_matches.items()
        ]

    else:
        # Default mode - show options
        show_available_options()
        return

    # Process predictions
    success_count = 0
    total_count = len(matches_to_predict)

    for prediction_group in matches_to_predict:
        league_name = prediction_group['league']
        matches = prediction_group['matches']

        print(f"\n🚀 Processing {league_name}...")
        print(f"📊 Matches: {len(matches)}")
        for home, away in matches:
            print(f"  • {home} vs {away}")

        if predict_matches(league_name, matches):
            success_count += 1
            print(f"✅ {league_name} completed successfully!")
        else:
            print(f"❌ {league_name} failed!")

    # Final summary
    print(f"\n🏆 PREDICTION SUMMARY")
    print(f"=" * 30)
    print(f"✅ Successful: {success_count}/{total_count}")
    print(f"❌ Failed: {total_count - success_count}/{total_count}")

    if success_count > 0:
        print(f"\n🎉 Predictions completed! Check the data/processed/[LEAGUE]/ directories for:")
        print(f"  • Markdown reports")
        print(f"  • Excel files")
        print(f"  • Visualizations (img/ folder)")
        print(f"  • Match logs (logs/ folder)")
    else:
        print(f"\n😞 No predictions were completed successfully.")

    logger.info("🏁 Main prediction process completed.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Process interrupted by user")
        logger.info("Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        logger.error(f"Unexpected error in main: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)
