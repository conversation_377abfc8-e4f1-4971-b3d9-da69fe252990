"""
Core model training functionality that ties together all components.
"""

import pandas as pd
import numpy as np
import logging
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.calibration import CalibratedClassifierCV
import tensorflow as tf
from imblearn.over_sampling import SMOTE
from typing import Dict, Optional, Union, List, Tuple, Any

from .constants import (
    STRING_COLUMNS,
    RF_N_ESTIMATORS,
    RF_MAX_DEPTH,
    RF_MIN_SAMPLES_SPLIT,
    RF_MIN_SAMPLES_LEAF,
    MIN_SAMPLES_FOR_SMOTE,
    TRAIN_SIZE,
    BATCH_SIZE,
    EPOCHS,
    DRAW_WEIGHT_MULTIPLIER,
    HOME_AWAY_WEIGHT_MULTIPLIER,
    SMOTE_K_NEIGHBORS
)
from .neural_network import create_three_way_model, get_callbacks
from .feature_analysis import analyze_feature_importance, analyze_neural_network_importance
from .visualization import (
    plot_confusion_matrix,
    plot_training_history,
    plot_class_distribution
)
from utils import safe_classification_report

logger = logging.getLogger(__name__)

def train_model(
    X: pd.DataFrame,
    y_dict: Dict[str, pd.Series],
    label_encoders: Dict[str, LabelEncoder]
) -> Dict[str, Dict]:
    """
    Train multiple models for different prediction types.
    """
    logger.info("Starting train_model function")
    logger.info(f"Shape of X: {X.shape}")

    if X.empty or not y_dict:
        logger.error("Empty input data for model training")
        return {}

    # Remove string columns and keep track of numeric columns
    numeric_columns = [col for col in X.columns if col not in STRING_COLUMNS]
    X_numeric = X[numeric_columns]

    # Split data chronologically
    train_size = int(TRAIN_SIZE * len(X_numeric))
    if train_size < 1:
        logger.error("Not enough samples for training")
        return {}

    X_train = X_numeric[:train_size]
    X_test = X_numeric[train_size:]

    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = pd.DataFrame(
        scaler.fit_transform(X_train),
        columns=X_train.columns
    )
    X_test_scaled = pd.DataFrame(
        scaler.transform(X_test),
        columns=X_test.columns
    )

    models = {}
    for pred_type, y in y_dict.items():
        logger.info(f"\nTraining model for {pred_type}")
        try:
            if y.empty:
                logger.warning(f"Empty target variable for {pred_type}")
                continue

            # Check if there's enough data for training
            unique_classes = y.nunique()
            if unique_classes < 2:
                logger.warning(f"Not enough unique classes for {pred_type} (found {unique_classes})")
                continue

            models[pred_type] = _train_single_model(
                pred_type=pred_type,
                X_train=X_train_scaled,
                X_test=X_test_scaled,
                y=y,
                train_size=train_size,
                numeric_columns=numeric_columns,
                label_encoders=label_encoders,
                scaler=scaler
            )
        except Exception as e:
            logger.error(f"Error training model for {pred_type}: {str(e)}")
            continue

    return models

def _train_single_model(
    pred_type: str,
    X_train: pd.DataFrame,
    X_test: pd.DataFrame,
    y: pd.Series,
    train_size: int,
    numeric_columns: List[str],
    label_encoders: Dict[str, LabelEncoder],
    scaler: StandardScaler
) -> Dict[str, Any]:
    """Train a single model for a specific prediction type."""
    try:
        # Split target variable
        y_train = y[:train_size]
        y_test = y[train_size:]

        if y_train.empty or y_test.empty:
            logger.warning(f"Empty train or test set for {pred_type}")
            return None

        # Ensure label encoder exists and encode the labels
        if pred_type not in label_encoders:
            label_encoders[pred_type] = LabelEncoder()
        encoder = label_encoders[pred_type]

        # Handle different types of target variables
        y_train_encoded, y_test_encoded = _encode_target_variables(
            pred_type, y_train, y_test, encoder
        )

        if len(np.unique(y_train_encoded)) < 2:
            logger.warning(f"Not enough unique classes in training data for {pred_type}")
            logger.warning(f"Training data distribution for {pred_type}: {np.unique(y_train_encoded, return_counts=True)}")
            logger.warning(f"Full data distribution for {pred_type}: {np.unique(y[:train_size], return_counts=True)}")
            return None

        # Plot class distribution
        plot_class_distribution(y_train, pred_type)

        if pred_type == "three_way":
            return _train_ensemble_three_way(
                X_train, X_test,
                y_train_encoded, y_test_encoded,
                encoder, numeric_columns,
                pred_type, scaler
            )
        else:
            return _train_random_forest(
                X_train, X_test,
                y_train_encoded, y_test_encoded,
                encoder, numeric_columns,
                pred_type, scaler
            )

    except Exception as e:
        logger.error(f"Error in _train_single_model for {pred_type}: {str(e)}")
        return None

def _encode_target_variables(
    pred_type: str,
    y_train: pd.Series,
    y_test: pd.Series,
    encoder: LabelEncoder
) -> Tuple[np.ndarray, np.ndarray]:
    """Encode target variables based on prediction type."""
    try:
        if y_train.empty or y_test.empty:
            raise ValueError("Empty target variable")

        if pred_type == "btts":
            # Map Yes/No directly to 1/0
            y_train_encoded = np.where(y_train == "Yes", 1, 0)
            y_test_encoded = np.where(y_test == "Yes", 1, 0)
            encoder.classes_ = np.array(["No", "Yes"])
        else:
            # Handle other prediction types
            unique_values = pd.concat([y_train, y_test]).astype(str).unique()
            if not hasattr(encoder, 'classes_') or len(set(unique_values) - set(encoder.classes_)) > 0:
                encoder.fit(unique_values)
            
            y_train_encoded = encoder.transform(y_train.astype(str))
            y_test_encoded = encoder.transform(y_test.astype(str))

        return y_train_encoded, y_test_encoded

    except Exception as e:
        logger.error(f"Error encoding target variables for {pred_type}: {str(e)}")
        raise


def _train_ensemble_three_way(
    X_train: pd.DataFrame,
    X_test: pd.DataFrame,
    y_train_encoded: np.ndarray,
    y_test_encoded: np.ndarray,
    encoder: LabelEncoder,
    numeric_columns: List[str],
    pred_type: str,
    scaler: StandardScaler
) -> Dict[str, Any]:
    """Train an ensemble of models for three-way prediction."""
    try:
        logger.info("Training ensemble model for three-way prediction")

        # Train multiple models
        models = {}

        # 1. Enhanced Neural Network
        nn_model = _train_neural_network(
            X_train, X_test, y_train_encoded, y_test_encoded,
            encoder, numeric_columns, pred_type, scaler
        )
        models['neural_network'] = nn_model

        # 2. XGBoost Model
        xgb_model = _train_xgboost_model(
            X_train, X_test, y_train_encoded, y_test_encoded,
            encoder, pred_type
        )
        models['xgboost'] = xgb_model

        # 3. Random Forest Model
        rf_model = _train_random_forest_model(
            X_train, X_test, y_train_encoded, y_test_encoded,
            encoder, pred_type
        )
        models['random_forest'] = rf_model

        # Create ensemble wrapper with optimized weights based on individual performance
        # XGBoost and Random Forest are performing much better than Neural Network
        ensemble_model = EnsembleModel(models, weights=[0.2, 0.5, 0.3])  # [NN, XGB, RF]

        # Evaluate ensemble
        ensemble_pred = ensemble_model.predict(X_test)
        ensemble_accuracy = _evaluate_model(y_test_encoded, ensemble_pred, encoder, pred_type)

        logger.info(f"Ensemble {pred_type} Model Accuracy: {ensemble_accuracy:.4f}")

        return {
            "model": ensemble_model,
            "scaler": scaler,
            "encoder": encoder,
            "accuracy": ensemble_accuracy,
            "feature_names": numeric_columns,
        }

    except Exception as e:
        logger.error(f"Error training ensemble model: {str(e)}")
        # Fallback to single neural network
        return _train_neural_network(
            X_train, X_test, y_train_encoded, y_test_encoded,
            encoder, numeric_columns, pred_type, scaler
        )

def _train_neural_network(
    X_train: pd.DataFrame,
    X_test: pd.DataFrame,
    y_train_encoded: np.ndarray,
    y_test_encoded: np.ndarray,
    encoder: LabelEncoder,
    numeric_columns: List[str],
    pred_type: str,
    scaler: StandardScaler
) -> Dict[str, Any]:
    """Train a neural network model for three-way prediction with enhanced class balancing."""
    try:
        # Apply SMOTE to balance classes before training neural network
        X_train_balanced, y_train_balanced = _apply_smote(X_train, y_train_encoded)

        # Convert to categorical
        y_train_cat = tf.keras.utils.to_categorical(y_train_balanced)
        y_test_cat = tf.keras.utils.to_categorical(y_test_encoded)

        # Create and train model
        model = create_three_way_model(X_train_balanced.shape[1], len(encoder.classes_))
        class_weights = _calculate_three_way_class_weights(y_train_balanced, encoder)

        # Split balanced data for validation (use last 20% as validation)
        val_split_idx = int(len(X_train_balanced) * 0.8)
        X_train_final = X_train_balanced[:val_split_idx]
        X_val = X_train_balanced[val_split_idx:]
        y_train_final = y_train_cat[:val_split_idx]
        y_val = y_train_cat[val_split_idx:]

        # Train with validation data and callbacks
        try:
            history = model.fit(
                X_train_final,
                y_train_final,
                epochs=EPOCHS,
                batch_size=BATCH_SIZE,
                validation_data=(X_val, y_val),
                class_weight=class_weights,
                callbacks=get_callbacks(),
                verbose=1,  # Show progress for debugging
                shuffle=True  # Shuffle training data
            )
        except Exception as callback_error:
            logger.warning(f"Training with callbacks failed: {callback_error}")
            # Fallback training without problematic callbacks
            history = model.fit(
                X_train_final,
                y_train_final,
                epochs=50,  # Reduced epochs for fallback
                batch_size=BATCH_SIZE,
                validation_data=(X_val, y_val),
                class_weight=class_weights,
                verbose=1,
                shuffle=True
            )

        # Evaluate
        y_pred = np.argmax(model.predict(X_test, verbose=0), axis=1)
        accuracy = _evaluate_model(y_test_encoded, y_pred, encoder, pred_type)

        # Analyze feature importance (optional, don't fail if it doesn't work)
        feature_importance = None
        try:
            feature_importance = analyze_neural_network_importance(
                model, X_test, numeric_columns, pred_type
            )
        except Exception as fi_error:
            logger.warning(f"Feature importance analysis failed: {fi_error}")

        # Plot training history (optional)
        try:
            plot_training_history(history.history, pred_type)
        except Exception as plot_error:
            logger.warning(f"Training history plotting failed: {plot_error}")

        return {
            "model": model,
            "accuracy": accuracy,
            "feature_names": numeric_columns,
            "y_test": y_test_encoded,
            "X_test": X_test,
            "scaler": scaler,
            "num_classes": len(encoder.classes_),
            "encoder": encoder,
            "history": history.history,
            "feature_importance": feature_importance,
        }

    except Exception as e:
        logger.error(f"Error training neural network for {pred_type}: {str(e)}")
        # Return a minimal valid structure instead of None
        return {
            "model": None,
            "accuracy": 0.0,
            "feature_names": numeric_columns,
            "y_test": y_test_encoded,
            "X_test": X_test,
            "scaler": scaler,
            "num_classes": len(encoder.classes_) if encoder else 3,
            "encoder": encoder,
            "history": {},
            "feature_importance": None,
        }

def _train_random_forest(
    X_train: pd.DataFrame,
    X_test: pd.DataFrame,
    y_train_encoded: np.ndarray,
    y_test_encoded: np.ndarray,
    encoder: LabelEncoder,
    numeric_columns: List[str],
    pred_type: str,
    scaler: StandardScaler
) -> Dict[str, Any]:
    """Train a random forest model."""
    try:
        class_weights = _calculate_prediction_weights(y_train_encoded, pred_type, encoder)
        
        model = RandomForestClassifier(
            n_estimators=RF_N_ESTIMATORS,
            max_depth=RF_MAX_DEPTH,
            min_samples_split=RF_MIN_SAMPLES_SPLIT,
            min_samples_leaf=RF_MIN_SAMPLES_LEAF,
            class_weight=class_weights,
            max_features="sqrt",
            random_state=42,
            bootstrap=True,
            oob_score=True,
        )

        # Apply SMOTE if applicable
        X_train_final, y_train_final = _apply_smote(X_train, y_train_encoded)
        
        # Train model
        model.fit(X_train_final, y_train_final)

        # Add probability calibration
        model = CalibratedClassifierCV(model, cv=5, method="sigmoid")
        model.fit(X_train_final, y_train_final)

        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = _evaluate_model(y_test_encoded, y_pred, encoder, pred_type)

        # Analyze feature importance
        feature_importance = analyze_feature_importance(
            model, X_test, y_test_encoded, numeric_columns, pred_type, encoder
        )

        return {
            "model": model,
            "accuracy": accuracy,
            "feature_names": numeric_columns,
            "y_test": y_test_encoded,
            "X_test": X_test,
            "scaler": scaler,
            "encoder": encoder,
            "feature_importance": feature_importance,
        }

    except Exception as e:
        logger.error(f"Error training random forest for {pred_type}: {str(e)}")
        return None

def _calculate_three_way_class_weights(
    y_train_encoded: np.ndarray,
    encoder: LabelEncoder
) -> Dict[int, float]:
    """Calculate enhanced class weights for three-way prediction with focus on Draw class."""
    try:
        class_counts = np.bincount(y_train_encoded)
        total_samples = len(y_train_encoded)

        # Log class distribution for debugging
        for i, count in enumerate(class_counts):
            class_name = encoder.classes_[i] if i < len(encoder.classes_) else f"Class_{i}"
            logger.info(f"Class {class_name}: {count} samples ({count/total_samples*100:.1f}%)")

        # Calculate base weights using inverse frequency
        class_weights = {
            i: total_samples / (len(class_counts) * count)
            for i, count in enumerate(class_counts)
        }

        # Use sklearn's balanced approach as base, then apply strategic adjustments
        from sklearn.utils.class_weight import compute_class_weight
        try:
            balanced_weights = compute_class_weight(
                'balanced',
                classes=np.unique(y_train_encoded),
                y=y_train_encoded
            )
            # Update class_weights with balanced approach
            for i, weight in enumerate(balanced_weights):
                class_weights[i] = weight
        except Exception as e:
            logger.warning(f"Could not compute balanced weights: {e}, using inverse frequency")

        # Apply strategic adjustments for three-way classification
        for i, class_name in enumerate(encoder.classes_):
            if class_name == "Draw":
                # Extra boost for Draw class (hardest to predict)
                class_weights[i] *= DRAW_WEIGHT_MULTIPLIER
            elif class_name in ["Home", "Away"]:
                # Moderate boost for Home/Away classes
                class_weights[i] *= HOME_AWAY_WEIGHT_MULTIPLIER

        # Smooth extreme weights to prevent training instability
        max_weight = max(class_weights.values())
        min_weight = min(class_weights.values())
        if max_weight / min_weight > 10:  # Cap extreme ratios
            cap_ratio = 8.0
            for i in class_weights:
                if class_weights[i] > min_weight * cap_ratio:
                    class_weights[i] = min_weight * cap_ratio

        logger.info(f"Final class weights: {class_weights}")
        return class_weights

    except Exception as e:
        logger.error(f"Error calculating three-way class weights: {str(e)}")
        return {i: 1.0 for i in range(len(encoder.classes_))}

def _calculate_prediction_weights(
    y_train: np.ndarray,
    pred_type: str,
    encoder: LabelEncoder
) -> Dict[int, float]:
    """Calculate class weights based on prediction type."""
    try:
        class_counts = np.bincount(y_train)
        total_samples = len(y_train)
        weights = {
            i: (total_samples / (len(class_counts) * count)) ** 2.0
            for i, count in enumerate(class_counts)
        }

        # Adjust weights based on prediction type
        if pred_type.startswith("over_under"):
            if "Under" in encoder.classes_:
                under_idx = np.where(encoder.classes_ == "Under")[0][0]
                weights[under_idx] *= 2.0
        elif pred_type == "btts":
            if "No" in encoder.classes_:
                no_idx = np.where(encoder.classes_ == "No")[0][0]
                weights[no_idx] *= 1.5

        return weights

    except Exception as e:
        logger.error(f"Error calculating prediction weights: {str(e)}")
        return {i: 1.0 for i in range(len(encoder.classes_))}

def _apply_smote(
    X_train: pd.DataFrame,
    y_train: np.ndarray
) -> Tuple[pd.DataFrame, np.ndarray]:
    """Apply enhanced SMOTE with better handling for severely imbalanced classes."""
    try:
        class_counts = np.bincount(y_train)
        min_samples = min(class_counts)
        max_samples = max(class_counts)

        logger.info(f"Original class distribution: {dict(enumerate(class_counts))}")

        # Apply conservative SMOTE only for severe imbalance
        imbalance_ratio = max_samples / min_samples
        if len(np.unique(y_train)) > 1 and min_samples >= MIN_SAMPLES_FOR_SMOTE and imbalance_ratio > 2.0:
            try:
                # Conservative sampling strategy - don't fully balance, just reduce imbalance
                target_size = int(min_samples * 1.8)  # More conservative multiplier
                sampling_strategy = {}

                for i, count in enumerate(class_counts):
                    if count < target_size:
                        sampling_strategy[i] = min(target_size, int(count * 1.5))  # Conservative increase

                if sampling_strategy:  # Only apply if there's something to oversample
                    k_neighbors = min(SMOTE_K_NEIGHBORS, min_samples - 1)
                    if k_neighbors > 0:
                        smote = SMOTE(
                            random_state=42,
                            sampling_strategy=sampling_strategy,
                            k_neighbors=k_neighbors,
                        )
                        X_resampled, y_resampled = smote.fit_resample(X_train, y_train)

                    new_class_counts = np.bincount(y_resampled)
                    logger.info(f"SMOTE applied. New class distribution: {dict(enumerate(new_class_counts))}")
                    return X_resampled, y_resampled
                else:
                    logger.info("No SMOTE needed - classes already balanced enough")
            except Exception as e:
                logger.warning(f"SMOTE failed, using original data: {str(e)}")
        else:
            logger.info(f"SMOTE not applied: min_samples={min_samples}, threshold={MIN_SAMPLES_FOR_SMOTE}")

        return X_train, y_train

    except Exception as e:
        logger.error(f"Error applying SMOTE: {str(e)}")
        return X_train, y_train

def _evaluate_model(
    y_test: np.ndarray,
    y_pred: np.ndarray,
    encoder: LabelEncoder,
    pred_type: str
) -> float:
    """Evaluate model and log results."""
    try:
        accuracy = np.mean(y_test == y_pred)
        
        y_test_original = encoder.inverse_transform(y_test)
        y_pred_original = encoder.inverse_transform(y_pred)

        logger.info(f"{pred_type.capitalize()} Model Accuracy: {accuracy:.4f}")
        logger.info("\nClassification Report:")
        logger.info(safe_classification_report(y_test_original, y_pred_original))

        # Create confusion matrix visualization
        plot_confusion_matrix(y_test_original, y_pred_original, pred_type)

        return accuracy

    except Exception as e:
        logger.error(f"Error evaluating model: {str(e)}")
        return 0.0


class EnsembleModel:
    """Ensemble model that combines multiple base models."""

    def __init__(self, models: Dict[str, Any], weights: List[float] = None):
        self.models = {k: v for k, v in models.items() if v is not None}
        self.weights = weights or [1.0 / len(self.models)] * len(self.models)
        self.model_names = list(self.models.keys())

        # Adjust weights if some models failed
        if len(self.weights) != len(self.models):
            self.weights = [1.0 / len(self.models)] * len(self.models)

    def predict(self, X):
        """Make ensemble prediction by weighted averaging."""
        if not self.models:
            # Fallback prediction
            return np.zeros(len(X), dtype=int)

        predictions = []

        for i, (name, model_info) in enumerate(self.models.items()):
            model = model_info['model']

            try:
                if hasattr(model, 'predict_proba'):
                    # Sklearn-style model
                    pred_proba = model.predict_proba(X)
                else:
                    # TensorFlow model
                    pred_proba = model.predict(X, verbose=0)

                predictions.append(pred_proba * self.weights[i])
            except Exception as e:
                logger.warning(f"Model {name} prediction failed: {e}")
                continue

        if not predictions:
            return np.zeros(len(X), dtype=int)

        # Average predictions
        ensemble_proba = np.sum(predictions, axis=0)
        return np.argmax(ensemble_proba, axis=1)

    def predict_proba(self, X):
        """Get ensemble probability predictions."""
        if not self.models:
            # Fallback: uniform probabilities
            return np.ones((len(X), 3)) / 3

        predictions = []

        for i, (name, model_info) in enumerate(self.models.items()):
            model = model_info['model']

            try:
                if hasattr(model, 'predict_proba'):
                    pred_proba = model.predict_proba(X)
                else:
                    pred_proba = model.predict(X, verbose=0)

                predictions.append(pred_proba * self.weights[i])
            except Exception as e:
                logger.warning(f"Model {name} prediction failed: {e}")
                continue

        if not predictions:
            return np.ones((len(X), 3)) / 3

        return np.sum(predictions, axis=0)


def _train_xgboost_model(
    X_train: pd.DataFrame,
    X_test: pd.DataFrame,
    y_train_encoded: np.ndarray,
    y_test_encoded: np.ndarray,
    encoder: LabelEncoder,
    pred_type: str
) -> Dict[str, Any]:
    """Train XGBoost model for three-way prediction."""
    try:
        from xgboost import XGBClassifier

        # Calculate class weights
        from sklearn.utils.class_weight import compute_class_weight
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(y_train_encoded),
            y=y_train_encoded
        )
        sample_weights = np.array([class_weights[y] for y in y_train_encoded])

        model = XGBClassifier(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='mlogloss',
            verbosity=0
        )

        model.fit(X_train, y_train_encoded, sample_weight=sample_weights)

        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = _evaluate_model(y_test_encoded, y_pred, encoder, pred_type)

        logger.info(f"XGBoost {pred_type} Model Accuracy: {accuracy:.4f}")

        return {
            "model": model,
            "accuracy": accuracy,
        }

    except ImportError:
        logger.warning("XGBoost not available, skipping XGBoost model")
        return None
    except Exception as e:
        logger.error(f"Error training XGBoost model: {str(e)}")
        return None


def _train_random_forest_model(
    X_train: pd.DataFrame,
    X_test: pd.DataFrame,
    y_train_encoded: np.ndarray,
    y_test_encoded: np.ndarray,
    encoder: LabelEncoder,
    pred_type: str
) -> Dict[str, Any]:
    """Train Random Forest model for three-way prediction."""
    try:
        from sklearn.ensemble import RandomForestClassifier

        model = RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            class_weight='balanced',
            random_state=42,
            n_jobs=-1
        )

        model.fit(X_train, y_train_encoded)

        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = _evaluate_model(y_test_encoded, y_pred, encoder, pred_type)

        logger.info(f"Random Forest {pred_type} Model Accuracy: {accuracy:.4f}")

        return {
            "model": model,
            "accuracy": accuracy,
        }

    except Exception as e:
        logger.error(f"Error training Random Forest model: {str(e)}")
        return None
