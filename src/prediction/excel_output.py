"""
Module for handling Excel output formatting and saving.
"""

import logging
import os
import openpyxl
from openpyxl.styles import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PatternFill
from openpyxl.utils import get_column_letter
from openpyxl.styles.numbers import FORMAT_PERCENTAGE_00
from typing import Dict, Any, List, Optional, Tuple

from .constants import (
    EXCEL_STYLES,
    EXCEL_HEADERS,
    MAX_GOALS_TO_CONSIDER
)

logger = logging.getLogger(__name__)

def save_predictions_to_excel(
    predictions: Dict[str, Any],
    league_name: str = ""
) -> None:
    """
    Save match predictions to an Excel file with enhanced formatting and additional analysis.

    Args:
        predictions: Dictionary containing predictions for each match
        league_name: Name of the league
    """
    logger.info("Starting Excel output process")
    _log_predictions(predictions, league_name)

    if not predictions:
        logger.error(f"No predictions to save for league {league_name}")
        return

    try:
        # Create workbook and sheets
        wb = openpyxl.Workbook()
        main_sheet = wb.active
        main_sheet.title = "Match Predictions"
        analysis_sheet = wb.create_sheet(title="Detailed Analysis")

        # Setup sheets
        _setup_main_sheet(main_sheet, league_name)
        _setup_analysis_sheet(analysis_sheet)

        # Process predictions
        _process_predictions(predictions, main_sheet, analysis_sheet)

        # Format sheets
        format_excel_sheet(main_sheet)
        format_excel_sheet(analysis_sheet)

        # Save workbook
        filename = _get_output_filename(league_name)
        wb.save(filename)
        logger.info(f"Predictions saved to {filename}")

    except Exception as e:
        logger.error(f"Error saving predictions to Excel: {str(e)}")

def format_excel_sheet(sheet, start_row: int = 2) -> None:
    """
    Format the Excel sheet with enhanced styles and conditional formatting.

    Args:
        sheet: Excel worksheet object
        start_row: Starting row for formatting (excluding header)
    """
    try:
        # Format headers
        _format_headers(sheet, start_row)

        # Format data rows
        _format_data_rows(sheet, start_row)

        # Auto-adjust column widths
        _adjust_column_widths(sheet)

        # Add alternating row colors
        _add_alternating_row_colors(sheet, start_row)

        # Freeze panes
        sheet.freeze_panes = sheet["A3"]

    except Exception as e:
        logger.error(f"Error formatting Excel sheet: {str(e)}")

def _log_predictions(predictions: Dict[str, Any], league_name: str) -> None:
    """Log prediction data for debugging."""
    logger.info(f"Processing predictions for {league_name}:")
    for match, pred in predictions.items():
        logger.info(f"\nMatch: {match}")
        # logger.info(f"Prediction data: {pred}")

def _get_output_filename(league_name: str) -> str:
    """Get the output filename for Excel file with league subdirectory."""
    # Create league-specific subdirectory
    league_dir = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        "data",
        "processed",
        league_name
    )
    os.makedirs(league_dir, exist_ok=True)
    return os.path.join(league_dir, f"{league_name}_match_predictions.xlsx")

def _setup_main_sheet(sheet, league_name: str) -> None:
    """Setup main predictions sheet."""
    # Add title
    sheet.merge_cells("A1:Z1")
    title_cell = sheet["A1"]
    title_cell.value = f"{league_name} - Match Predictions"
    title_cell.font = Font(**EXCEL_STYLES['title']['font'])
    title_cell.alignment = Alignment(**EXCEL_STYLES['title']['alignment'])

    # Add headers
    sheet.append(EXCEL_HEADERS['main'])

def _setup_analysis_sheet(sheet) -> None:
    """Setup detailed analysis sheet."""
    sheet.append(EXCEL_HEADERS['analysis'])

def _process_predictions(
    predictions: Dict[str, Any],
    main_sheet,
    analysis_sheet
) -> None:
    """Process predictions and add to Excel sheets."""
    for match, pred in predictions.items():
        if not isinstance(pred, dict):
            logger.warning(f"Skipping invalid prediction for match {match}: {pred}")
            continue

        try:
            if " vs " in match:
                home_team, away_team = match.split(" vs ")
            else:
                home_team, away_team = match, "N/A"

            # Process main predictions
            main_row = _prepare_main_row(home_team, away_team, pred)
            main_sheet.append(main_row)

            # Process analysis
            analysis_row = _prepare_analysis_row(home_team, away_team, pred)
            analysis_sheet.append(analysis_row)

        except Exception as e:
            logger.error(f"Error processing match {match}: {str(e)}")

def _prepare_main_row(
    home_team: str,
    away_team: str,
    pred: Dict[str, Any]
) -> List[Any]:
    """Prepare row data for main predictions sheet."""
    main_pred = pred["main_predictions"]
    
    # Get probabilities
    three_way_probs = _get_market_probabilities(main_pred, "three_way")
    double_chance_probs = _get_market_probabilities(main_pred, "double_chance")
    btts_probs = _get_market_probabilities(main_pred, "btts")

    # Get over/under predictions for all thresholds
    ou_1_5 = _get_over_under_prediction(main_pred, "1.5")
    ou_2_5 = _get_over_under_prediction(main_pred, "2.5")
    ou_3_5 = _get_over_under_prediction(main_pred, "3.5")

    # Get most likely score
    most_likely_score = _get_most_likely_score(pred.get("score_predictions", {}))

    # Get expected goals from score_predictions
    score_predictions = pred.get("score_predictions", {})
    home_xg = float(score_predictions.get("home_expected_goals", 0) or 0)
    away_xg = float(score_predictions.get("away_expected_goals", 0) or 0)

    return [
        home_team,
        away_team,
        main_pred.get("three_way", {}).get("prediction", "N/A"),
        three_way_probs.get("Home", 0),
        three_way_probs.get("Draw", 0),
        three_way_probs.get("Away", 0),
        pred.get("confidence_levels", {}).get("three_way", {}).get("confidence_level", "N/A"),
        pred.get("risk_assessment", {}).get("overall", {}).get("risk_level", "N/A"),
        main_pred.get("double_chance", {}).get("prediction", "N/A"),
        double_chance_probs.get("Home or Draw", 0),
        double_chance_probs.get("Away or Draw", 0),
        double_chance_probs.get("Home or Away", 0),
        main_pred.get("btts", {}).get("prediction", "N/A"),
        btts_probs.get("Yes", 0),
        btts_probs.get("No", 0),
        *ou_1_5,
        *ou_2_5,
        *ou_3_5,
        home_xg,
        away_xg,
        most_likely_score[0],
        float(most_likely_score[1] or 0),
        pred.get("form_data_valid_str", "N/A"),
    ]

def _prepare_analysis_row(
    home_team: str,
    away_team: str,
    pred: Dict[str, Any]
) -> List[Any]:
    """Prepare row data for analysis sheet."""
    score_predictions = pred.get("score_predictions", {})
    most_likely_scores = score_predictions.get("most_likely_scores", {})
    
    # Get top 3 scores
    top_scores = list(most_likely_scores.items())[:3]
    top_scores_str = ", ".join([f"{score}({prob:.1%})" for score, prob in top_scores])

    # Calculate BTTS probability
    btts_prob = sum(
        prob
        for score, prob in most_likely_scores.items()
        if int(score.split("-")[0]) > 0 and int(score.split("-")[1]) > 0
    )

    return [
        f"{home_team} vs {away_team}",
        pred.get("confidence_levels", {}).get("three_way", {}).get("confidence_level", "N/A"),
        pred.get("risk_assessment", {}).get("overall", {}).get("risk_level", "N/A"),
        top_scores_str,
        f"{btts_prob:.1%}",
        f"{1 - btts_prob:.1%}",  # Home clean sheet
        f"{1 - btts_prob:.1%}",  # Away clean sheet
        f"{score_predictions.get('expected_total_goals', 0):.2f}",
        pred.get("risk_assessment", {}).get("overall", {}).get("notes", ""),
    ]

def _format_headers(sheet, start_row: int) -> None:
    """Format sheet headers."""
    header_style = EXCEL_STYLES['header']
    for cell in sheet[start_row]:
        cell.font = Font(**header_style['font'])
        cell.fill = PatternFill(**header_style['fill'])
        cell.alignment = Alignment(**header_style['alignment'])

def _format_data_rows(sheet, start_row: int) -> None:
    """Format data rows with appropriate styles."""
    for row in sheet.iter_rows(min_row=start_row + 1, max_row=sheet.max_row):
        for idx, cell in enumerate(row, 1):
            # Center align all cells
            cell.alignment = Alignment(horizontal="center", vertical="center")

            # Apply number formatting
            _apply_cell_formatting(cell, idx)

            # Apply conditional formatting
            _apply_conditional_formatting(cell)

def _adjust_column_widths(sheet) -> None:
    """Auto-adjust column widths."""
    for column in sheet.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)

        for cell in column:
            try:
                cell_length = len(str(cell.value))
                max_length = max(max_length, cell_length)
            except:
                pass

        adjusted_width = (max_length + 2) * 1.2
        sheet.column_dimensions[column_letter].width = min(adjusted_width, 30)

def _add_alternating_row_colors(sheet, start_row: int) -> None:
    """Add alternating row colors."""
    alt_fill = PatternFill(**EXCEL_STYLES['alternating_row']['fill'])
    for row_idx in range(start_row + 1, sheet.max_row + 1):
        if row_idx % 2 == 0:
            for cell in sheet[row_idx]:
                if not cell.fill.start_color.index:  # Only if no other fill is applied
                    cell.fill = alt_fill

def _apply_cell_formatting(cell, column_idx: int) -> None:
    """Apply appropriate formatting to cell based on content and column."""
    if isinstance(cell.value, (int, float)):
        # Expected goals columns (25 and 26 are the indices for Expected Goals)
        if column_idx in [25, 26]:
            cell.number_format = "0.00"
        # Other numeric columns that should be percentages
        elif 0 <= cell.value <= 1 and column_idx not in [25, 26]:
            cell.number_format = FORMAT_PERCENTAGE_00
        else:
            cell.number_format = "0.00"

def _apply_conditional_formatting(cell) -> None:
    """Apply conditional formatting based on cell value."""
    if cell.value in EXCEL_STYLES['confidence_colors']:
        cell.fill = PatternFill(**EXCEL_STYLES['confidence_colors'][cell.value]['fill'])
    elif cell.value in EXCEL_STYLES['risk_colors']:
        cell.fill = PatternFill(**EXCEL_STYLES['risk_colors'][cell.value]['fill'])

def _get_market_probabilities(
    main_pred: Dict[str, Any],
    market: str
) -> Dict[str, float]:
    """Get probabilities for a specific market."""
    return main_pred.get(market, {}).get("probabilities", {})

def _get_over_under_prediction(
    main_pred: Dict[str, Any],
    threshold: str
) -> Tuple[str, float, float]:
    """Get over/under prediction and probabilities for a specific threshold."""
    # Convert threshold to underscore format (e.g., "1.5" -> "1_5")
    threshold_key = threshold.replace(".", "_")
    market = f"over_under_{threshold_key}"
    pred_data = main_pred.get(market, {})
    pred = pred_data.get("prediction", "")
    probs = pred_data.get("probabilities", {})

    # Use the original threshold format for probability keys (e.g., "Over 1.5")
    over_prob = float(probs.get(f"Over {threshold}", 0) or 0)
    under_prob = float(probs.get(f"Under {threshold}", 0) or 0)

    return pred, over_prob, under_prob

def _get_most_likely_score(
    score_predictions: Dict[str, Any]
) -> Tuple[str, float]:
    """Get the most likely score and its probability."""
    most_likely_scores = score_predictions.get("most_likely_scores", {})
    return next(iter(most_likely_scores.items())) if most_likely_scores else ("N/A", 0)
