"""
Module for calculating expected goals and related metrics.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, <PERSON>ple, Union

from .constants import (
    SEASON_WEIGHT,
    RECENT_FORM_WEIGHT,
    H2H_WEIGHT,
    FORM_IMPACT_WEIGHT,
    POINTS_DIFF_HOME_WEIGHT,
    POINTS_DIFF_AWAY_WEIGHT,
    STRONG_HOME_BOOST,
    NORMAL_HOME_BOOST,
    NORMAL_AWAY_REDUCTION,
    BASE_AWAY_ADJUSTMENT,
    AWAY_STRENGTH_FACTOR,
    STRONG_ATTACK_THRESHOLD,
    MAX_HOME_XG_STRONG,
    MAX_HOME_XG_NORMAL,
    MAX_AWAY_XG_STRONG,
    MAX_AWAY_XG_NORMAL,
    MIN_HOME_XG_STRONG,
    MIN_HOME_XG_NORMAL,
    MIN_AWAY_XG_STRONG,
    MIN_AWAY_XG_NORMAL
)

logger = logging.getLogger(__name__)

def calculate_expected_goals(
    home_stats: Union[pd.Series, Dict[str, float]],
    away_stats: Union[pd.Series, Dict[str, float]],
    league_stats_dict: Dict[str, float]
) -> Tuple[float, float]:
    """
    Calculate expected goals based on team statistics and league averages.
    
    Args:
        home_stats: Statistics for the home team (pandas Series or dict)
        away_stats: Statistics for the away team (pandas Series or dict)
        league_stats_dict: Dictionary of league-wide statistics
    
    Returns:
        Tuple containing expected goals for home and away teams
    
    Note:
        The calculation considers multiple factors including:
        - Season-long scoring and conceding rates
        - Recent form and scoring rates
        - Team attack and defense strengths
        - Home/away performance
        - Head-to-head history
        - Points difference in the league
    """
    logger.debug("Starting expected goals calculation")
    
    # Get scoring rates (blend season and recent form)
    home_scoring = (SEASON_WEIGHT * float(home_stats.get("goals_scored_per_match_home", 0)) + 
                   RECENT_FORM_WEIGHT * float(home_stats.get("home_recent_scoring_rate", 0)))
    away_scoring = (SEASON_WEIGHT * float(away_stats.get("goals_scored_per_match_away", 0)) + 
                   RECENT_FORM_WEIGHT * float(away_stats.get("away_recent_scoring_rate", 0)))
    
    logger.debug(f"Scoring rates - Home: {home_scoring:.2f}, Away: {away_scoring:.2f}")
    
    # Get defensive rates (blend season and recent form)
    home_conceding = (SEASON_WEIGHT * float(home_stats.get("goals_conceded_per_match_home", 0)) + 
                     RECENT_FORM_WEIGHT * float(home_stats.get("home_recent_conceding_rate", 0)))
    away_conceding = (SEASON_WEIGHT * float(away_stats.get("goals_conceded_per_match_away", 0)) + 
                     RECENT_FORM_WEIGHT * float(away_stats.get("away_recent_conceding_rate", 0)))
    
    logger.debug(f"Conceding rates - Home: {home_conceding:.2f}, Away: {away_conceding:.2f}")
    
    # Get league averages
    league_home_avg = float(league_stats_dict.get("home_goals_per_match", 1.5))
    league_away_avg = float(league_stats_dict.get("away_goals_per_match", 1.0))
    
    logger.debug(f"League averages - Home: {league_home_avg:.2f}, Away: {league_away_avg:.2f}")
    
    # Calculate attack strengths properly using the same logic as feature engineering
    # Since raw stats don't have calculated attack strengths, we need to calculate them

    # Home attack strength calculation
    home_goals_scored_home = float(home_stats.get("goals_scored_per_match_home", 0))
    home_ppg = float(home_stats.get("ppg_last_8", 1.5))
    home_form_factor = home_ppg / 2.0
    home_base_attack = home_goals_scored_home / league_home_avg if league_home_avg > 0 else 1.0
    home_form_weight = np.exp(home_form_factor - 1)
    home_attack = home_base_attack * (0.6 + 0.4 * home_form_weight) * 1.15  # HOME_VENUE_BOOST

    # Away attack strength calculation
    away_goals_scored_away = float(away_stats.get("goals_scored_per_match_away", 0))
    away_ppg = float(away_stats.get("ppg_last_8", 1.5))
    away_form_factor = away_ppg / 2.0
    away_base_attack = away_goals_scored_away / league_away_avg if league_away_avg > 0 else 1.0
    away_form_weight = np.exp(away_form_factor - 1)
    away_attack = away_base_attack * (0.6 + 0.4 * away_form_weight) * 0.85  # AWAY_VENUE_REDUCTION

    # Normalize to reasonable range (same as feature engineering)
    home_attack = 0.5 + 1.5 / (1 + np.exp(-2 * (home_attack - 1)))
    away_attack = 0.5 + 1.5 / (1 + np.exp(-2 * (away_attack - 1)))
    
    logger.debug(f"Attack strengths - Home: {home_attack:.2f}, Away: {away_attack:.2f}")
    
    # Calculate defense strengths (lower conceding rate = better defense)
    home_defense = 2 / (home_conceding if home_conceding > 0 else 1)
    away_defense = 2 / (away_conceding if away_conceding > 0 else 1)
    
    logger.debug(f"Defense strengths - Home: {home_defense:.2f}, Away: {away_defense:.2f}")
    
    # Get form factors with PPG
    home_form = float(home_stats.get("ppg_last_8", 1.5)) / 3.0
    away_form = float(away_stats.get("ppg_last_8", 1.5)) / 3.0
    
    logger.debug(f"Form factors - Home: {home_form:.2f}, Away: {away_form:.2f}")
    
    # Calculate base expected goals with more realistic formula
    # Use a simpler, more interpretable approach

    # Home xG: team's scoring rate adjusted by opponent's defense and form
    # Better opponent defense (higher away_defense) should REDUCE home xG
    home_xg = (home_scoring * (2.0 / away_defense) *
               (1 + FORM_IMPACT_WEIGHT * (home_form - away_form)))

    # Away xG: team's scoring rate adjusted by opponent's defense and form
    # Better opponent defense (higher home_defense) should REDUCE away xG
    away_xg = (away_scoring * (2.0 / home_defense) *
               (1 + FORM_IMPACT_WEIGHT * (away_form - home_form)))
    
    logger.debug(f"Base xG - Home: {home_xg:.2f}, Away: {away_xg:.2f}")
    
    # Apply home/away adjustment with team strength consideration
    home_strength = float(home_stats.get("home_points_per_game", 1.5)) / 3.0
    away_strength = float(away_stats.get("away_points_per_game", 1.5)) / 3.0
    
    logger.info(f"Team strengths - Home: {home_strength:.2f}, Away: {away_strength:.2f}")
    
    # Dynamic home advantage based on team strengths
    if home_strength > away_strength:
        home_xg *= STRONG_HOME_BOOST
        away_xg *= NORMAL_AWAY_REDUCTION
    else:
        home_xg *= NORMAL_HOME_BOOST
        away_xg *= (BASE_AWAY_ADJUSTMENT + (away_strength - home_strength) * AWAY_STRENGTH_FACTOR)
    
    logger.debug(f"After home advantage adjustment - Home xG: {home_xg:.2f}, Away xG: {away_xg:.2f}")
    
    # Consider H2H history
    h2h_games = float(home_stats.get("h2h_total_matches", 0))
    if h2h_games > 0:
        h2h_home_avg = float(home_stats.get("h2h_team_a_goals", 0)) / h2h_games
        h2h_away_avg = float(home_stats.get("h2h_team_b_goals", 0)) / h2h_games
        home_xg = (1 - H2H_WEIGHT) * home_xg + H2H_WEIGHT * h2h_home_avg
        away_xg = (1 - H2H_WEIGHT) * away_xg + H2H_WEIGHT * h2h_away_avg
        
        logger.debug(f"After H2H adjustment - Home xG: {home_xg:.2f}, Away xG: {away_xg:.2f}")
    
    # Consider points difference with dynamic impact
    points_diff = float(home_stats.get("points_difference", 0))
    if abs(points_diff) > 0.1:  # Only apply if significant difference
        if points_diff > 0:  # Home team better
            home_xg *= (1 + points_diff * POINTS_DIFF_HOME_WEIGHT)
            away_xg *= (1 - points_diff * POINTS_DIFF_AWAY_WEIGHT)
        else:  # Away team better
            home_xg *= (1 + points_diff * POINTS_DIFF_AWAY_WEIGHT)
            away_xg *= (1 - points_diff * POINTS_DIFF_HOME_WEIGHT)
        
        logger.debug(f"After points difference adjustment - Home xG: {home_xg:.2f}, Away xG: {away_xg:.2f}")
    
    # Set reasonable ranges based on team strength
    max_home = MAX_HOME_XG_STRONG if home_attack > STRONG_ATTACK_THRESHOLD else MAX_HOME_XG_NORMAL
    max_away = MAX_AWAY_XG_STRONG if away_attack > STRONG_ATTACK_THRESHOLD else MAX_AWAY_XG_NORMAL
    
    # Dynamic minimum values based on team strength
    min_home = MIN_HOME_XG_STRONG if home_attack > STRONG_ATTACK_THRESHOLD else MIN_HOME_XG_NORMAL
    min_away = MIN_AWAY_XG_STRONG if away_attack > STRONG_ATTACK_THRESHOLD else MIN_AWAY_XG_NORMAL
    
    # Ensure reasonable ranges
    home_xg = np.clip(home_xg, min_home, max_home)
    away_xg = np.clip(away_xg, min_away, max_away)
    
    logger.info(f"Final expected goals - Home: {home_xg:.2f}, Away: {away_xg:.2f}")
    
    return home_xg, away_xg
