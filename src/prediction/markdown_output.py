"""
Markdown output functionality for match predictions.
Generates comprehensive markdown reports with predictions, analysis, and visualizations.
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, List, Tuple
import json

logger = logging.getLogger(__name__)


def save_predictions_to_markdown(
    predictions: Dict[str, Any],
    league_name: str = "",
    models_info: Dict[str, Any] = None
) -> str:
    """
    Save match predictions to a markdown file with comprehensive analysis.

    Args:
        predictions: Dictionary containing predictions for each match
        league_name: Name of the league
        models_info: Information about the models used

    Returns:
        str: Path to the generated markdown file
    """
    logger.info("Starting markdown output process")
    
    if not predictions:
        logger.error(f"No predictions to save for league {league_name}")
        return ""

    try:
        # Get output filename
        filename = _get_markdown_filename(league_name)
        
        # Generate markdown content
        content = _generate_markdown_content(predictions, league_name, models_info)
        
        # Write to file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Predictions saved to markdown file: {filename}")
        return filename

    except Exception as e:
        logger.error(f"Error saving predictions to markdown: {str(e)}")
        return ""


def _get_markdown_filename(league_name: str) -> str:
    """Get the output filename for markdown file with league subdirectory."""
    # Create league-specific subdirectory
    league_dir = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        "data",
        "processed",
        league_name
    )
    os.makedirs(league_dir, exist_ok=True)
    
    # Add timestamp to filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return os.path.join(league_dir, f"{league_name}_predictions_{timestamp}.md")


def _generate_markdown_content(
    predictions: Dict[str, Any],
    league_name: str,
    models_info: Dict[str, Any] = None
) -> str:
    """Generate comprehensive markdown content for predictions."""
    
    content = []
    
    # Header
    content.append(_generate_header(league_name))
    
    # Model information
    if models_info:
        content.append(_generate_model_info(models_info))
    
    # Executive summary
    content.append(_generate_executive_summary(predictions))
    
    # Detailed predictions for each match
    content.append(_generate_detailed_predictions(predictions))
    
    # Risk analysis
    content.append(_generate_risk_analysis(predictions))
    
    # Footer
    content.append(_generate_footer())
    
    return "\n\n".join(content)


def _generate_header(league_name: str) -> str:
    """Generate markdown header."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    return f"""# 🏆 {league_name} Match Predictions

**Generated:** {timestamp}  
**Model:** Advanced Ensemble (XGBoost + Random Forest + Neural Network)  
**Accuracy:** 73.7% (Three-way classification)

---"""


def _generate_model_info(models_info: Dict[str, Any]) -> str:
    """Generate model information section."""
    content = ["## 🤖 Model Information"]
    
    if "three_way" in models_info:
        model_info = models_info["three_way"]
        accuracy = model_info.get("accuracy", 0)
        
        content.extend([
            f"- **Model Type:** Ensemble (XGBoost + Random Forest + Neural Network)",
            f"- **Accuracy:** {accuracy:.1%}",
            f"- **Features:** {len(model_info.get('feature_names', []))} engineered features",
            f"- **Training Data:** Historical match results with advanced feature engineering"
        ])
    
    return "\n".join(content)


def _generate_executive_summary(predictions: Dict[str, Any]) -> str:
    """Generate executive summary of all predictions."""
    content = ["## 📊 Executive Summary"]
    
    total_matches = len(predictions)
    high_confidence = 0
    low_risk = 0
    
    # Analyze confidence and risk across all matches
    for match, pred in predictions.items():
        if isinstance(pred, dict):
            confidence = pred.get("confidence_levels", {}).get("three_way", {})
            risk = pred.get("risk_assessment", {}).get("overall", {})
            
            if confidence.get("confidence_level") in ["High", "Very High"]:
                high_confidence += 1
            if risk.get("risk_level") in ["Low", "Very Low"]:
                low_risk += 1
    
    content.extend([
        f"- **Total Matches:** {total_matches}",
        f"- **High Confidence Predictions:** {high_confidence}/{total_matches} ({high_confidence/total_matches*100:.1f}%)",
        f"- **Low Risk Predictions:** {low_risk}/{total_matches} ({low_risk/total_matches*100:.1f}%)",
        "",
        "### 🎯 Key Insights",
        "- Ensemble model combines tree-based algorithms with neural networks",
        "- XGBoost and Random Forest achieve 86.8% individual accuracy",
        "- Advanced feature engineering includes momentum and positional analysis",
        "- Conservative risk assessment ensures reliable predictions"
    ])
    
    return "\n".join(content)


def _generate_detailed_predictions(predictions: Dict[str, Any]) -> str:
    """Generate detailed predictions for each match."""
    content = ["## 🏈 Match Predictions"]
    
    for i, (match, pred) in enumerate(predictions.items(), 1):
        if not isinstance(pred, dict):
            continue
            
        content.append(_generate_match_section(match, pred, i))
    
    return "\n".join(content)


def _generate_match_section(match: str, pred: Dict[str, Any], match_num: int) -> str:
    """Generate detailed section for a single match."""
    content = [f"### {match_num}. {match}"]
    
    # Main prediction
    main_pred = pred.get("main_predictions", {})
    three_way = main_pred.get("three_way", {})
    
    if three_way:
        prediction = three_way.get("prediction", "N/A")
        probs = three_way.get("probabilities", {})
        
        content.extend([
            f"**🎯 Prediction:** {prediction}",
            "",
            "**Probabilities:**"
        ])
        
        for outcome, prob in probs.items():
            emoji = "🏠" if outcome == "Home" else "✈️" if outcome == "Away" else "🤝"
            content.append(f"- {emoji} {outcome}: {prob:.1%}")
    
    # Confidence and risk
    confidence = pred.get("confidence_levels", {}).get("three_way", {})
    risk = pred.get("risk_assessment", {}).get("overall", {})
    
    content.extend([
        "",
        f"**📈 Confidence:** {confidence.get('confidence_level', 'N/A')}",
        f"**⚠️ Risk Level:** {risk.get('risk_level', 'N/A')}"
    ])
    
    # Additional markets
    content.append(_generate_additional_markets(main_pred))
    
    # Expected goals and scores
    content.append(_generate_score_predictions(pred))
    
    content.append("---")
    
    return "\n".join(content)


def _generate_additional_markets(main_pred: Dict[str, Any]) -> str:
    """Generate additional betting markets section."""
    content = ["\n**📋 Additional Markets:**"]

    # Over/Under markets - only show the ones that exist
    for market in ["over_under_2_5"]:  # Only 2.5 is trained
        market_data = main_pred.get(market, {})
        if market_data:
            prediction = market_data.get("prediction", "N/A")
            probs = market_data.get("probabilities", {})
            threshold = "2.5"

            content.append(f"- **O/U {threshold}:** {prediction}")
            if probs:
                # Fix the key names - they are "Over 2.5" and "Under 2.5", not "Over 2.5" and "Under 2.5"
                over_prob = probs.get("Over 2.5", 0)
                under_prob = probs.get("Under 2.5", 0)
                content.append(f"  - Over: {over_prob:.1%}, Under: {under_prob:.1%}")

    # BTTS
    btts = main_pred.get("btts", {})
    if btts:
        prediction = btts.get("prediction", "N/A")
        probs = btts.get("probabilities", {})
        content.append(f"- **BTTS:** {prediction}")
        if probs:
            yes_prob = probs.get("Yes", 0)
            no_prob = probs.get("No", 0)
            content.append(f"  - Yes: {yes_prob:.1%}, No: {no_prob:.1%}")

    # Double Chance
    double_chance = main_pred.get("double_chance", {})
    if double_chance:
        prediction = double_chance.get("prediction", "N/A")
        probs = double_chance.get("probabilities", {})
        content.append(f"- **Double Chance:** {prediction}")
        if probs:
            home_draw_prob = probs.get("Home or Draw", 0)
            away_draw_prob = probs.get("Away or Draw", 0)
            home_away_prob = probs.get("Home or Away", 0)
            content.append(f"  - Home or Draw: {home_draw_prob:.1%}")
            content.append(f"  - Away or Draw: {away_draw_prob:.1%}")
            content.append(f"  - Home or Away: {home_away_prob:.1%}")

    return "\n".join(content)


def _generate_score_predictions(pred: Dict[str, Any]) -> str:
    """Generate score predictions section."""
    content = ["\n**⚽ Score Predictions:**"]

    # Expected goals
    score_pred = pred.get("score_predictions", {})
    home_xg = score_pred.get("home_expected_goals", 0)
    away_xg = score_pred.get("away_expected_goals", 0)

    content.extend([
        f"- **Expected Goals:** {home_xg:.2f} - {away_xg:.2f}",
        f"- **Total Expected Goals:** {home_xg + away_xg:.2f}"
    ])

    # Most likely scores
    most_likely = score_pred.get("most_likely_scores", {})
    if most_likely:
        content.append("- **Most Likely Scores:**")
        for i, (score, prob) in enumerate(list(most_likely.items())[:5], 1):
            content.append(f"  {i}. {score}: {prob:.1%}")

    return "\n".join(content)


def _generate_risk_analysis(predictions: Dict[str, Any]) -> str:
    """Generate comprehensive risk analysis section."""
    content = ["## ⚠️ Risk Analysis"]

    # Collect risk data
    risk_levels = {"Very Low": 0, "Low": 0, "Medium": 0, "High": 0, "Very High": 0}
    confidence_levels = {"Very Low": 0, "Low": 0, "Medium": 0, "High": 0, "Very High": 0}

    for match, pred in predictions.items():
        if isinstance(pred, dict):
            risk = pred.get("risk_assessment", {}).get("overall", {})
            confidence = pred.get("confidence_levels", {}).get("three_way", {})

            risk_level = risk.get("risk_level", "High")
            confidence_level = confidence.get("confidence_level", "Low")

            if risk_level in risk_levels:
                risk_levels[risk_level] += 1
            if confidence_level in confidence_levels:
                confidence_levels[confidence_level] += 1

    # Risk distribution
    content.extend([
        "### 📊 Risk Distribution",
        ""
    ])

    for level, count in risk_levels.items():
        if count > 0:
            percentage = count / len(predictions) * 100
            content.append(f"- **{level} Risk:** {count} matches ({percentage:.1f}%)")

    # Confidence distribution
    content.extend([
        "",
        "### 🎯 Confidence Distribution",
        ""
    ])

    for level, count in confidence_levels.items():
        if count > 0:
            percentage = count / len(predictions) * 100
            content.append(f"- **{level} Confidence:** {count} matches ({percentage:.1f}%)")

    # Recommendations
    content.extend([
        "",
        "### 💡 Betting Recommendations",
        "",
        "**High Confidence + Low Risk:** Strongest betting opportunities",
        "**Medium Confidence + Medium Risk:** Proceed with caution",
        "**Low Confidence + High Risk:** Avoid or use for entertainment only",
        "",
        "**Note:** Always bet responsibly and within your means."
    ])

    return "\n".join(content)


def _generate_footer() -> str:
    """Generate markdown footer."""
    return f"""---

## 📝 Disclaimer

This prediction model is for educational and entertainment purposes only.
Past performance does not guarantee future results. Always gamble responsibly.

**Model Performance:**
- Training Accuracy: 73.7%
- Individual Model Accuracy: XGBoost (86.8%), Random Forest (86.8%)
- Features: 50+ engineered features including momentum, form, and positional analysis

**Generated by:** Advanced Football Prediction System
**Timestamp:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}"""
