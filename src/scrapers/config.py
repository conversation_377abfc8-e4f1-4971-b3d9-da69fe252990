import os
import importlib
import logging
import sys

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
]
# Note: Logging configuration removed to avoid conflicts with main scripts
# Each main script should configure its own logging as needed

# Get the absolute path of the current file (config.py)
current_dir = os.path.dirname(os.path.abspath(__file__))

# Add the league_configs directory to the Python path
league_configs_dir = os.path.join(current_dir, 'league_configs')
sys.path.insert(0, league_configs_dir)

# Add the parent directory (src) to the Python path
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# List all league config files (sorted alphabetically)
league_files = sorted([f[:-3] for f in os.listdir(league_configs_dir) if f.endswith(".py") and f != "__init__.py"])

# Import all league configurations
LEAGUE_CONFIGS = {}
for module_name in league_files:
    try:
        module = importlib.import_module(module_name)

        # Assuming each league file has a LEAGUE_CONFIGS dictionary
        if hasattr(module, 'LEAGUE_CONFIGS'):
            for league_key, league_config in module.LEAGUE_CONFIGS.items():
                if "TEAM_NAME_MAPPING" not in league_config:
                    league_config["TEAM_NAME_MAPPING"] = {}
                LEAGUE_CONFIGS[league_key] = league_config
        else:
            logging.warning(f"No LEAGUE_CONFIGS found in {module_name}")

    except Exception as e:
        logging.error(f"Error importing {module_name}: {str(e)}")

# Define CURRENT_CONFIG if it is not already defined
CURRENT_CONFIG = LEAGUE_CONFIGS
