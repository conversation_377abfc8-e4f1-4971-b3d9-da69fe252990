import logging
import argparse
import argcomplete
from utils import (
    save_league_stats_to_csv,
    save_team_stats_to_csv,
    save_results_to_csv,
    save_head_to_head_stats_to_csv,
    save_recent_results_to_csv,
    save_league_table_to_csv,
    get_data_directory,
)
from league_stats import get_league_stats
from team_stats import get_team_stats
from results_scraper import scrape_all_results, RateLimitedScraper, USER_AGENTS
# from head_to_head import get_head_to_head_stats # This is no longer needed
# from h2h_batch_scraper import run_h2h_batch_scraping # This is no longer needed
from league_table import get_league_table
from recent_results_scraper import scrape_recent_results_for_league
from config import LEAGUE_CONFIGS
import pickle
import os
import pandas as pd
import numpy as np

# Note: Logging configuration removed to avoid conflicts with main scripts
# Each main script should configure its own logging as needed

CHECKPOINT_FILE = "checkpoint.pkl"

# Configure scrapers with different rate limits for different types of requests
LEAGUE_SCRAPER = RateLimitedScraper(
    requests_per_window=5,
    time_window_seconds=60,
    base_delay=10.0,
    max_retries=30,
    user_agents=USER_AGENTS,
)

TEAM_SCRAPER = RateLimitedScraper(
    requests_per_window=5,
    time_window_seconds=60,
    base_delay=10.0,
    max_retries=30,
    user_agents=USER_AGENTS,
)

def save_checkpoint(state):
    with open(CHECKPOINT_FILE, "wb") as f:
        pickle.dump(state, f)

def load_checkpoint():
    if os.path.exists(CHECKPOINT_FILE):
        with open(CHECKPOINT_FILE, "rb") as f:
            return pickle.load(f)
    return None

def replace_nan_with_zero(data):
    if isinstance(data, (pd.DataFrame, pd.Series)):
        return data.fillna(0)
    elif isinstance(data, np.ndarray):
        return np.nan_to_num(data, nan=0)
    elif isinstance(data, dict):
        return {k: replace_nan_with_zero(v) for k, v in data.items()}
    elif isinstance(data, (list, tuple)):
        return [replace_nan_with_zero(v) for v in data]
    elif pd.isna(data):
        return 0
    else:
        return data

def main():
    parser = argparse.ArgumentParser(description="Scrape sports data (excluding H2H). H2H is now handled by head_to_head.py.")
    
    # Scope arguments
    parser.add_argument("--league", type=str, help="Target a specific league. If omitted, operations apply to all configured leagues.")
    parser.add_argument("--team", type=str, help="Target a specific team for team stats. Requires --league.")
    parser.add_argument("--match", type=str, help="Target a specific matchup (e.g., 'TeamA vs TeamB'). Requires --league.")

    # Operation arguments
    op_group = parser.add_argument_group('Data Operation Flags')
    op_group.add_argument("--force-all-data", action='store_true', help="Overwrite ALL data types (league, team, results, table, H2H).")
    op_group.add_argument("--refresh-league-data", action='store_true', help="Overwrite league_stats, team_stats, and league_table.")
    op_group.add_argument("--refresh-results", action='store_true', help="Overwrite historical match results.")
    op_group.add_argument("--refresh-h2h", action='store_true', help="Overwrite H2H stats and their derived recent results.")
    op_group.add_argument("--refresh-recent-results", action='store_true', help="Overwrite recent results data independently from H2H.")
    op_group.add_argument("--incremental-results", action='store_true', help="Incrementally update historical match results.")
    op_group.add_argument("--incremental-h2h", action='store_true', help="Incrementally update H2H stats and their derived recent results.")
    op_group.add_argument("--incremental-all-updatable", action='store_true', help="Convenience flag to run both --incremental-results and --incremental-h2h.")
    
    argcomplete.autocomplete(parser)
    args = parser.parse_args()

    # Validate team and match usage
    if args.team and not args.league:
        parser.error("--team argument requires --league to be specified.")
    if args.match and not args.league:
        parser.error("--match argument requires --league to be specified.")

    # Handle convenience flag
    if args.incremental_all_updatable:
        args.incremental_results = True
        args.incremental_h2h = True

    any_refresh_or_incremental_flag_set = (
        args.force_all_data or
        args.refresh_league_data or
        args.refresh_results or
        args.refresh_h2h or
        args.refresh_recent_results or
        args.incremental_results or
        args.incremental_h2h or
        args.incremental_all_updatable
    )

    # Determine global operation settings
    g_process_league_data = False
    g_overwrite_league_data = False
    g_process_results_data = False
    g_overwrite_results_data = False
    g_increment_results_data = False
    g_process_recent_results_data = False
    g_overwrite_recent_results_data = False
    is_default_full_overwrite_behavior = False

    if args.force_all_data:
        logging.info("Operation mode: --force-all-data. Overwriting all non-H2H data types for the scope.")
        g_process_league_data = True
        g_overwrite_league_data = True
        g_process_results_data = True
        g_overwrite_results_data = True
        g_process_recent_results_data = True
        g_overwrite_recent_results_data = True
    elif not any_refresh_or_incremental_flag_set:
        logging.info("Operation mode: No specific operation flags. Defaulting to full overwrite for the scope (excluding H2H).")
        is_default_full_overwrite_behavior = True
        g_process_league_data = True
        g_overwrite_league_data = True
        g_process_results_data = True
        g_overwrite_results_data = True
        g_process_recent_results_data = True
        g_overwrite_recent_results_data = True
    else:
        logging.info("Operation mode: Specific refresh/incremental flags detected.")
        if args.refresh_league_data:
            g_process_league_data = True
            g_overwrite_league_data = True
        if args.refresh_results:
            g_process_results_data = True
            g_overwrite_results_data = True
        elif args.incremental_results:
            g_process_results_data = True
            g_increment_results_data = True
        if args.refresh_recent_results:
            g_process_recent_results_data = True
            g_overwrite_recent_results_data = True

    checkpoint = load_checkpoint()
    start_league = 0
    use_checkpoint_for_this_run = not args.league and (args.force_all_data or is_default_full_overwrite_behavior)

    league_items = list(LEAGUE_CONFIGS.items())

    if args.league:
        league_items = [(name, config) for name, config in league_items if name.lower() == args.league.lower()]
        if not league_items:
            logging.error(f"Target league '{args.league}' not found or misconfigured.")
            return

    elif use_checkpoint_for_this_run and checkpoint:
        start_league = checkpoint.get("league_index", 0)
        # ... (rest of your checkpoint logic)

    is_full_run_and_intended_overwrite = not args.league and (args.force_all_data or is_default_full_overwrite_behavior)

    for league_index in range(start_league, len(league_items)):
        league_name, league_config = league_items[league_index]
        try:
            logging.info(f"Processing league: {league_name}")

            if g_process_league_data:
                # Fetch and save league stats
                league_stats_url = league_config.get("LEAGUE_STATS_URL")
                if league_stats_url:
                    league_stats = get_league_stats(league_stats_url, LEAGUE_SCRAPER)
                    save_league_stats_to_csv(league_stats, league_name, f"{league_name}_league_stats.csv")
                
                # Fetch and save team stats
                team_urls = league_config.get("TEAM_URLS", {})
                team_stats_dict = {}
                for team, url in team_urls.items():
                    team_stats = get_team_stats(url, team, TEAM_SCRAPER)
                    team_stats_dict[team] = team_stats
                save_team_stats_to_csv(team_stats_dict, league_name, f"{league_name}_team_stats.csv")
                
                # Fetch and save league table
                league_table_url = league_config.get("LEAGUE_TABLE_URL")
                if league_table_url:
                    league_table = get_league_table(league_table_url, LEAGUE_SCRAPER)
                    save_league_table_to_csv(league_table, league_name, f"{league_name}_league_table.csv")
            
            if g_process_results_data:
                # Fetch and save match results
                team_urls = league_config.get("TEAM_URLS", {})
                if team_urls:
                    start_date_dt = None
                    if g_increment_results_data:
                        # For incremental updates, determine the last date from existing results
                        from pathlib import Path
                        existing_results_file = Path(get_data_directory(league_name)) / f"{league_name}_results.csv"
                        if existing_results_file.exists():
                            try:
                                import pandas as pd
                                from datetime import datetime
                                existing_df = pd.read_csv(existing_results_file)
                                if not existing_df.empty and 'Date' in existing_df.columns:
                                    # Parse the last date from the existing data
                                    last_date_str = existing_df['Date'].iloc[-1]
                                    # Try different date formats
                                    for fmt in ["%b %d, %Y", "%d %b %Y", "%Y-%m-%d", "%d/%m/%Y"]:
                                        try:
                                            start_date_dt = datetime.strptime(last_date_str, fmt)
                                            logging.info(f"Incremental update from date: {start_date_dt.strftime('%Y-%m-%d')}")
                                            break
                                        except ValueError:
                                            continue
                                    if not start_date_dt:
                                        logging.warning(f"Could not parse last date '{last_date_str}', doing full refresh")
                            except Exception as e:
                                logging.warning(f"Error reading existing results for incremental update: {e}")
                    
                    results = scrape_all_results(team_urls, start_date_dt)
                    save_results_to_csv(results, league_name, f"{league_name}_results.csv", 
                                       append=g_increment_results_data)
            
            # Process recent results data
            if g_process_recent_results_data:
                # Fetch and save recent match results
                recent_results = scrape_recent_results_for_league(league_config)
                save_recent_results_to_csv(recent_results, league_name, f"{league_name}_recent_results.csv", append=False)

            # Process H2H data if specified
            if args.refresh_h2h or args.incremental_h2h:
                logging.info(f"H2H processing for {league_name} not yet implemented")
                # TODO: Add H2H processing logic here
                # This would call the head_to_head.py functionality
                pass

            if is_full_run_and_intended_overwrite:
                save_checkpoint({"league_index": league_index + 1, "league_name_at_checkpoint": league_name})

        except Exception as e:
            logging.error(f"Error processing league {league_name}: {e}")
            logging.exception(f"Traceback for {league_name}:")
            if is_full_run_and_intended_overwrite:
                save_checkpoint({"league_index": league_index, "league_name_at_checkpoint": league_name})
            logging.error("Stopping script due to error.")
            break
            
    if is_full_run_and_intended_overwrite and os.path.exists(CHECKPOINT_FILE):
        # ... (your checkpoint removal logic) ...
        pass

if __name__ == "__main__":
    main()
