"""
This module contains utility functions for the betting project.
It includes functions for logging setup, path handling, and other helper functions.
"""

import os
import sys
import logging
from sklearn.metrics import classification_report

logger = logging.getLogger(__name__)


def setup_logging():
    """
    Set up logging configuration only if not already configured.
    This prevents conflicts with main scripts that set up their own logging.

    Returns:
    None
    """
    # Only configure logging if no handlers are already set up
    if not logging.getLogger().handlers:
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        logger = logging.getLogger(__name__)
        logger.info("Logging setup complete.")
    else:
        logger = logging.getLogger(__name__)
        logger.debug("Logging already configured, skipping setup.")


def get_script_path():
    """
    Get the absolute path of the current script.

    Returns:
    str: The absolute path of the current script.
    """
    return os.path.abspath(sys.argv[0])


def get_script_dir():
    """
    Get the directory of the current script.

    Returns:
    str: The directory of the current script.
    """
    return os.path.dirname(get_script_path())


def add_parent_dir_to_path():
    """
    Add the parent directory of the current script to the Python path.

    Returns:
    None
    """
    current_dir = get_script_dir()
    parent_dir = os.path.dirname(current_dir)
    sys.path.insert(0, parent_dir)
    logger.info(f"Parent directory added to Python path: {parent_dir}")


def get_image_directory(league_name: str = ""):
    """
    Get the path to the image directory, creating it if it doesn't exist.

    Args:
        league_name: Optional league name to create league-specific subdirectory

    Returns:
        str: Path to the image directory
    """
    if league_name:
        img_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "data", "processed", league_name, "img"
        )
    else:
        img_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "data", "processed", "img"
        )
    os.makedirs(img_dir, exist_ok=True)
    return img_dir


def standardize_team_name(name, team_name_mapping):
    """
    Standardize team names using a mapping, with case-insensitive matching.

    Args:
    name (str): The original team name.
    team_name_mapping (dict): A dictionary mapping original names to standardized names.

    Returns:
    str: The standardized team name.
    """
    # Create case-insensitive mapping
    case_insensitive_mapping = {k.lower(): v for k, v in team_name_mapping.items()}
    return case_insensitive_mapping.get(name.lower(), name)


def safe_classification_report(y_true, y_pred, label_encoder=None):
    """
    Generate a classification report in a safe manner, handling potential issues.

    Args:
    y_true (array-like): True labels.
    y_pred (array-like): Predicted labels.
    label_encoder (LabelEncoder, optional): Label encoder used to transform labels.

    Returns:
    str: Classification report.
    """
    try:
        if label_encoder:
            y_true = label_encoder.inverse_transform(y_true)
            y_pred = label_encoder.inverse_transform(y_pred)
        report = classification_report(y_true, y_pred)
        return report
    except Exception as e:
        logger.error(f"Error generating classification report: {e}")
        return "Error generating classification report"


# Add any other utility functions here
