#!/usr/bin/env python3
"""
Test script to run predictions on 10 interesting EPL matches.
This script demonstrates the full prediction pipeline with organized output.
"""

import sys
import os
import logging
from datetime import datetime
import pandas as pd
import numpy as np
import traceback

# Clear any cached modules to ensure fresh imports
modules_to_clear = [
    'prediction.expected_goals',
    'prediction.core',
    'prediction',
    'feature_engineering.core',
    'feature_engineering',
    'model_training.core',
    'model_training'
]

for module in modules_to_clear:
    if module in sys.modules:
        del sys.modules[module]

# Add src to path
sys.path.append('src')

# Import from the working test script approach
from data_loading import load_data, get_available_leagues
from scrapers.config import LEAGUE_CONFIGS
from feature_engineering import prepare_features
from model_training import train_model
from prediction import predict_match
from prediction.analysis import analyze_prediction_confidence, assess_prediction_risk, generate_prediction_summary
from prediction.excel_output import save_predictions_to_excel
from prediction.markdown_output import save_predictions_to_markdown
from utils import get_image_directory
from model_training.visualization import plot_feature_importance, plot_training_history
from analysis.visualization import plot_prediction_distribution
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/epl_10_matches_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def generate_prediction_visualizations(predictions, models, league_name):
    """Generate visualizations for predictions and models."""
    try:
        img_dir = get_image_directory(league_name)

        # 1. Generate prediction distribution plots
        logger.info("Creating prediction distribution plots...")
        for pred_type in ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']:
            if pred_type in models:
                # Extract predictions for this type
                pred_values = []
                for match_name, match_data in predictions.items():
                    main_pred = match_data.get('main_predictions', {})
                    if pred_type in main_pred:
                        pred_values.append(main_pred[pred_type].get('prediction', ''))

                if pred_values:
                    # Create prediction distribution plot
                    plt.figure(figsize=(10, 6))
                    pred_counts = pd.Series(pred_values).value_counts()
                    sns.barplot(x=pred_counts.index, y=pred_counts.values)
                    plt.title(f'Prediction Distribution - {pred_type.replace("_", " ").title()}')
                    plt.xlabel('Prediction')
                    plt.ylabel('Count')
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    plt.savefig(os.path.join(img_dir, f'prediction_distribution_{pred_type}.png'))
                    plt.close()

        # 2. Generate feature importance plots (if available)
        logger.info("Creating feature importance plots...")
        for pred_type, model_data in models.items():
            if hasattr(model_data, 'feature_importances_'):
                # For tree-based models
                feature_names = model_data.feature_names_in_ if hasattr(model_data, 'feature_names_in_') else [f'feature_{i}' for i in range(len(model_data.feature_importances_))]
                importance_df = pd.DataFrame({
                    'feature': feature_names,
                    'importance': model_data.feature_importances_
                }).sort_values('importance', ascending=False)

                plot_feature_importance(importance_df, pred_type, "Ensemble Model")

        # 3. Generate summary statistics plot
        logger.info("Creating prediction summary plot...")
        create_prediction_summary_plot(predictions, img_dir)

        logger.info(f"✅ Generated visualizations in {img_dir}")

    except Exception as e:
        logger.error(f"❌ Error generating visualizations: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

def create_prediction_summary_plot(predictions, img_dir):
    """Create a summary plot showing prediction confidence across all matches."""
    try:
        # Extract confidence data
        confidence_data = []
        for match_name, match_data in predictions.items():
            main_pred = match_data.get('main_predictions', {})
            for pred_type, pred_data in main_pred.items():
                probs = pred_data.get('probabilities', {})
                if probs:
                    max_prob = max(probs.values())
                    confidence_data.append({
                        'match': match_name.split(' vs ')[0][:10] + '...',  # Shorten match names
                        'prediction_type': pred_type.replace('_', ' ').title(),
                        'confidence': max_prob
                    })

        if confidence_data:
            df = pd.DataFrame(confidence_data)

            # Create heatmap of confidence levels
            plt.figure(figsize=(14, 8))
            pivot_df = df.pivot(index='match', columns='prediction_type', values='confidence')
            sns.heatmap(pivot_df, annot=True, fmt='.2f', cmap='RdYlGn',
                       cbar_kws={'label': 'Prediction Confidence'})
            plt.title('Prediction Confidence Heatmap Across All Matches')
            plt.xlabel('Prediction Type')
            plt.ylabel('Match')
            plt.xticks(rotation=45)
            plt.yticks(rotation=0)
            plt.tight_layout()
            plt.savefig(os.path.join(img_dir, 'prediction_confidence_heatmap.png'))
            plt.close()

    except Exception as e:
        logger.error(f"Error creating summary plot: {str(e)}")

# Define 10 interesting EPL matches to test
TEST_MATCHES = [
    ("Liverpool", "Arsenal"),           # Title contenders
    ("Manchester City", "Chelsea"),     # Big 6 clash
    ("Tottenham", "Manchester Utd"),    # Traditional rivals
    ("Newcastle Utd", "Brighton"),      # Mid-table battle
    ("Aston Villa", "Nottm Forest"),    # European hopefuls
    ("Brentford", "Fulham"),           # London derby
    ("West Ham Utd", "Crystal Palace"), # London rivals
    ("Bournemouth", "Wolverhampton"),   # Relegation battle
    ("Everton", "Leicester City"),      # Struggling teams
    ("Southampton", "Ipswich Town"),    # Bottom table clash
]

def main():
    """Run predictions on 10 EPL matches with comprehensive output."""
    logger.info("=" * 80)
    logger.info("🏆 EPL 10 MATCHES PREDICTION TEST")
    logger.info("=" * 80)
    logger.info(f"Test started at: {datetime.now()}")
    
    try:
        # Step 1: Load data
        logger.info("\n" + "=" * 50)
        logger.info("📊 STEP 1: Loading EPL Data")
        logger.info("=" * 50)
        
        league_name = "ENGLAND_PREMIER_LEAGUE"

        # Check if league is available
        available_leagues = get_available_leagues()
        if league_name not in available_leagues:
            logger.error(f"❌ {league_name} not found in available leagues")
            return

        # Get league config
        league_config = LEAGUE_CONFIGS.get(league_name)
        if not league_config:
            logger.error(f"❌ No configuration found for {league_name}")
            return

        # Load data using the working approach
        data_tuple, data_info = load_data(league_name, league_config)

        if data_tuple is None:
            logger.error("Failed to load data")
            return

        # Unpack data tuple
        results, team_stats, league_stats, h2h_stats, league_table = data_tuple
        data = {
            'results': results,
            'team_stats': team_stats,
            'league_stats': league_stats,
            'h2h_stats': h2h_stats,
            'league_table': league_table
        }
        
        if not data or not all(key in data for key in ['results', 'team_stats', 'league_stats', 'h2h_stats', 'league_table']):
            logger.error("Failed to load required data")
            return
        
        logger.info("✅ Data loaded successfully:")
        logger.info(f"   - Results: {len(data['results'])} matches")
        logger.info(f"   - Team stats: {len(data['team_stats'])} teams")
        logger.info(f"   - League stats: {len(data['league_stats'])} statistics")
        logger.info(f"   - H2H stats: {len(data['h2h_stats'])} matchups")
        logger.info(f"   - League table: {len(data['league_table'])} teams")
        
        # Step 2: Prepare features
        logger.info("\n" + "=" * 50)
        logger.info("🔧 STEP 2: Preparing Features")
        logger.info("=" * 50)
        
        prepared_data = prepare_features(
            data['results'], data['team_stats'], data['league_stats'],
            data['h2h_stats'], data['league_table'],
            league_config.get('COLUMN_MAPPING', {}),
            league_config.get('TEAM_NAME_MAPPING', {})
        )
        
        if prepared_data is None or prepared_data.empty:
            logger.error("Failed to prepare features")
            return
        
        logger.info(f"✅ Features prepared: {prepared_data.shape}")
        
        # Step 3: Train models
        logger.info("\n" + "=" * 50)
        logger.info("🤖 STEP 3: Training Models")
        logger.info("=" * 50)
        
        # Prepare data for training (following working script approach)
        logger.info("Preparing training data...")

        # Check for NaN values
        nan_columns = prepared_data.columns[prepared_data.isna().any()].tolist()
        if nan_columns:
            logger.warning(f"⚠️ NaN values found in columns: {nan_columns[:5]}...")
            # Fill NaN values
            prepared_data = prepared_data.fillna(0)

        # Create target variables
        from sklearn.preprocessing import LabelEncoder

        # Create y_dict and label_encoders
        y_dict = {}
        label_encoders = {}

        # Three-way prediction
        if 'result' in prepared_data.columns:
            le_three_way = LabelEncoder()
            y_dict['three_way'] = pd.Series(le_three_way.fit_transform(prepared_data['result']))
            label_encoders['three_way'] = le_three_way

        # Over/Under predictions for all thresholds
        if 'total_goals' in prepared_data.columns:
            # Over/Under 1.5
            le_ou_1_5 = LabelEncoder()
            ou_1_5_labels = (prepared_data['total_goals'] > 1.5).astype(str)
            y_dict['over_under_1_5'] = pd.Series(le_ou_1_5.fit_transform(ou_1_5_labels))
            label_encoders['over_under_1_5'] = le_ou_1_5

            # Over/Under 2.5
            le_ou_2_5 = LabelEncoder()
            ou_2_5_labels = (prepared_data['total_goals'] > 2.5).astype(str)
            y_dict['over_under_2_5'] = pd.Series(le_ou_2_5.fit_transform(ou_2_5_labels))
            label_encoders['over_under_2_5'] = le_ou_2_5

            # Over/Under 3.5
            le_ou_3_5 = LabelEncoder()
            ou_3_5_labels = (prepared_data['total_goals'] > 3.5).astype(str)
            y_dict['over_under_3_5'] = pd.Series(le_ou_3_5.fit_transform(ou_3_5_labels))
            label_encoders['over_under_3_5'] = le_ou_3_5

        # BTTS prediction
        if 'btts' in prepared_data.columns:
            # Pass raw string values to model training (it will handle encoding internally)
            y_dict['btts'] = prepared_data['btts']  # Keep as "Yes"/"No" strings
            label_encoders['btts'] = LabelEncoder()  # Will be fitted in model training

        # Remove target columns and non-numeric columns from features
        # CRITICAL: Also exclude encoded target variables to prevent target leakage
        exclude_cols = ['result', 'total_goals', 'home_goals', 'away_goals',
                       'home_team', 'away_team', 'date', 'season',
                       'three_way', 'three_way_encoded',
                       'over_under_1_5', 'over_under_1_5_encoded',
                       'over_under_2_5', 'over_under_2_5_encoded',
                       'over_under_3_5', 'over_under_3_5_encoded',
                       'btts', 'btts_encoded']
        feature_cols = [col for col in prepared_data.columns
                       if col not in exclude_cols]
        X = prepared_data[feature_cols]

        # Convert all columns to numeric, replacing any remaining strings with 0
        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)

        logger.info(f"Training data shape: {X.shape}")
        logger.info(f"Target variables: {list(y_dict.keys())}")
        logger.info(f"Expected target variables: ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']")
        logger.info(f"Feature columns: {len(feature_cols)}")
        logger.info(f"✅ Excluded target leakage features: three_way_encoded, over_under_*_encoded, btts_encoded")

        # Train models
        logger.info("Training models...")
        # Ensure X is a DataFrame for the training function
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X, columns=feature_cols)

        models = train_model(X, y_dict, label_encoders)

        if not models:
            logger.error("❌ No models trained successfully")
            return

        # Log model performance
        logger.info("✅ Models trained successfully:")
        for pred_type, model_info in models.items():
            if model_info:
                logger.info(f"   - {pred_type}: {type(model_info.get('model', 'Unknown')).__name__}")
                logger.info(f"     Features: {len(model_info.get('feature_names', []))}")
            else:
                logger.warning(f"⚠️ {pred_type} model training incomplete")
        
        if not models:
            logger.error("No models trained successfully")
            return
        
        # Step 4: Run predictions on all matches
        logger.info("\n" + "=" * 50)
        logger.info("🎯 STEP 4: Running Match Predictions")
        logger.info("=" * 50)
        
        all_predictions = {}
        successful_predictions = 0
        
        for i, (home_team, away_team) in enumerate(TEST_MATCHES, 1):
            logger.info(f"\n--- Match {i}/10: {home_team} vs {away_team} ---")
            
            try:
                # Make prediction
                logger.debug(f"Calling predict_match for {home_team} vs {away_team}")
                try:
                    result = predict_match(
                        models=models,
                        home_team=home_team,
                        away_team=away_team,
                        team_stats=data['team_stats'],
                        league_stats=data['league_stats'],
                        h2h_stats=data['h2h_stats'],
                        league_table=data['league_table'],
                        column_mapping={},
                        feature_names=models.get('three_way', {}).get('feature_names', []) if models.get('three_way') is not None else [],
                        avg_goals_per_match=2.5,
                        label_encoders={pred_type: models[pred_type].get('encoder') for pred_type in models.keys() if models.get(pred_type) is not None},
                        log_features=False
                    )
                except Exception as predict_error:
                    logger.error(f"❌ Exception in predict_match: {str(predict_error)}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    continue

                logger.debug(f"predict_match returned: {type(result)}")

                # Debug: Check what predict_match returned
                if result is None:
                    logger.error(f"❌ predict_match returned None")
                    continue
                elif not isinstance(result, tuple) or len(result) != 3:
                    logger.error(f"❌ predict_match returned unexpected format: {type(result)}, length: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                    continue

                predictions, error_msg, correct_scores = result

                if error_msg:
                    logger.error(f"❌ Prediction failed: {error_msg}")
                    continue

                if predictions is None:
                    logger.error(f"❌ Predictions is None")
                    continue
                
                # Analyze confidence and risk
                main_predictions = predictions.get('main_predictions', {})
                confidence_analysis = analyze_prediction_confidence(main_predictions)
                risk_assessment = assess_prediction_risk(main_predictions, confidence_analysis)
                
                # Generate comprehensive summary
                match_summary = generate_prediction_summary(
                    main_predictions, correct_scores, confidence_analysis,
                    risk_assessment=risk_assessment
                )

                # Add expected goals from the predictions
                if 'score_predictions' not in match_summary:
                    match_summary['score_predictions'] = {}

                # Get expected goals from the predictions structure
                expected_goals = predictions.get('expected_goals', {})
                match_summary['score_predictions']['home_expected_goals'] = expected_goals.get('home', 0.0)
                match_summary['score_predictions']['away_expected_goals'] = expected_goals.get('away', 0.0)

                # Store prediction
                match_key = f"{home_team} vs {away_team}"
                all_predictions[match_key] = match_summary
                successful_predictions += 1
                
                # Log detailed prediction results (same format as single prediction test)
                logger.info("✅ Prediction completed successfully!")
                logger.info("📊 PREDICTION RESULTS:")
                logger.info(f"Match: {home_team} vs {away_team}")

                # Log all prediction types with detailed probabilities
                if "main_predictions" in predictions:
                    for pred_type, prediction in predictions["main_predictions"].items():
                        logger.info(f"\n{pred_type.upper()}:")
                        if isinstance(prediction, dict):
                            if "prediction" in prediction:
                                logger.info(f"   Prediction: {prediction['prediction']}")
                            if "probabilities" in prediction:
                                logger.info("   Probabilities:")
                                for outcome, prob in prediction["probabilities"].items():
                                    logger.info(f"     {outcome}: {prob:.3f}")

                # Log expected goals
                if "expected_goals" in predictions:
                    xg = predictions["expected_goals"]
                    logger.info(f"\nExpected Goals:")
                    logger.info(f"   Home: {xg['home']:.2f}")
                    logger.info(f"   Away: {xg['away']:.2f}")

                # Log top correct scores
                if correct_scores:
                    logger.info(f"\nTop Correct Scores:")
                    sorted_scores = sorted(correct_scores.items(), key=lambda x: x[1], reverse=True)
                    for score, prob in sorted_scores[:5]:
                        logger.info(f"   {score}: {prob:.3f}")

                # Log confidence analysis
                logger.info(f"\n📈 CONFIDENCE ANALYSIS:")
                for pred_type, conf_info in confidence_analysis.items():
                    if isinstance(conf_info, dict):
                        logger.info(f"{pred_type.upper()}:")
                        logger.info(f"   Confidence Level: {conf_info.get('confidence_level', 'N/A')}")
                        logger.info(f"   Max Probability: {conf_info.get('max_probability', 0):.3f}")
                        logger.info(f"   Prediction Strength: {conf_info.get('prediction_strength', 'N/A')}")

                # Log risk assessment
                logger.info(f"\n⚠️ RISK ASSESSMENT:")
                for pred_type, risk_info in risk_assessment.items():
                    if isinstance(risk_info, dict) and pred_type != "overall":
                        logger.info(f"{pred_type.upper()}:")
                        logger.info(f"   Risk Level: {risk_info.get('risk_level', 'N/A')}")
                        if 'factors' in risk_info:
                            logger.info("   Risk Factors:")
                            for factor, value in risk_info['factors'].items():
                                logger.info(f"     {factor}: {value}")

                # Log overall risk
                overall_risk = risk_assessment.get("overall", {})
                if overall_risk:
                    logger.info(f"\nOVERALL RISK:")
                    logger.info(f"   Risk Level: {overall_risk.get('risk_level', 'N/A')}")
                    logger.info(f"   Risk Score: {overall_risk.get('risk_score', 0.0):.3f}")

                logger.info("\n" + "="*50)
                
            except Exception as e:
                logger.error(f"❌ Error predicting {home_team} vs {away_team}: {str(e)}")
                continue
        
        logger.info(f"\n✅ Completed {successful_predictions}/{len(TEST_MATCHES)} predictions successfully")
        
        if not all_predictions:
            logger.error("No successful predictions to save")
            return
        
        # Step 5: Save outputs
        logger.info("\n" + "=" * 50)
        logger.info("💾 STEP 5: Saving Outputs")
        logger.info("=" * 50)
        
        # Save Excel file
        logger.info("Saving Excel file...")
        save_predictions_to_excel(all_predictions, league_name)
        
        # Save Markdown file
        logger.info("Saving Markdown file...")
        markdown_file = save_predictions_to_markdown(all_predictions, league_name, models)
        
        # Create images directory and generate visualizations
        img_dir = get_image_directory(league_name)
        logger.info(f"Images directory created: {img_dir}")

        # Generate visualizations
        logger.info("Generating visualizations...")
        generate_prediction_visualizations(all_predictions, models, league_name)
        logger.info("✅ Visualizations generated successfully")
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 EPL 10 MATCHES TEST COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        logger.info(f"✅ Predictions completed for {successful_predictions} matches")
        logger.info(f"📊 Excel file saved to: data/processed/{league_name}/")
        logger.info(f"📝 Markdown file saved to: {markdown_file}")
        logger.info(f"🖼️ Images directory: {img_dir}")
        logger.info(f"🕒 Test completed at: {datetime.now()}")
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        raise

if __name__ == "__main__":
    main()
