#!/usr/bin/env python3
"""
Debug test to check what data is being passed to expected_goals in the 10-match test
"""

import sys
import os
import logging
from datetime import datetime

# Clear any cached modules (same as 10-match test)
modules_to_clear = [
    'prediction.expected_goals',
    'prediction.core',
    'prediction',
    'feature_engineering.core',
    'feature_engineering',
    'model_training.core',
    'model_training'
]

for module in modules_to_clear:
    if module in sys.modules:
        del sys.modules[module]

# Add src to path (same as 10-match test)
sys.path.append('src')

# Import exactly as 10-match test does
from data_loading import load_data
from scrapers.config import LEAGUE_CONFIGS

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_debug_data():
    """Test what data is being loaded and passed to expected_goals"""
    
    logger.info("=== DEBUG DATA TEST ===")
    
    # Load data exactly as 10-match test does
    league_config = LEAGUE_CONFIGS['ENGLAND_PREMIER_LEAGUE']
    data_tuple = load_data('ENGLAND_PREMIER_LEAGUE', league_config)
    
    if data_tuple is None:
        logger.error("Failed to load data")
        return
    
    # Check data structure
    logger.info(f"Data structure: {type(data_tuple)}, length: {len(data_tuple)}")

    if len(data_tuple) == 2:
        # New format: (tuple_of_dataframes, league_stats_dict)
        dataframes_tuple, league_stats_dict = data_tuple
        results, team_stats, league_stats, h2h_stats, league_table = dataframes_tuple
    else:
        # Old format: tuple of dataframes
        results, team_stats, league_stats, h2h_stats, league_table = data_tuple
        league_stats_dict = {}
    
    logger.info(f"Team stats shape: {team_stats.shape}")
    logger.info(f"Team stats columns: {list(team_stats.columns)}")
    
    # Check Liverpool stats
    liverpool_row = team_stats[team_stats['Team'] == 'Liverpool']
    if not liverpool_row.empty:
        liverpool_stats = liverpool_row.iloc[0]
        logger.info("=== LIVERPOOL STATS ===")
        logger.info(f"goals_scored_per_match_home: {liverpool_stats.get('goals_scored_per_match_home', 'NOT FOUND')}")
        logger.info(f"goals_conceded_per_match_home: {liverpool_stats.get('goals_conceded_per_match_home', 'NOT FOUND')}")
        logger.info(f"home_recent_scoring_rate: {liverpool_stats.get('home_recent_scoring_rate', 'NOT FOUND')}")
        logger.info(f"home_recent_conceding_rate: {liverpool_stats.get('home_recent_conceding_rate', 'NOT FOUND')}")
        logger.info(f"avg_goals_scored_last_8: {liverpool_stats.get('avg_goals_scored_last_8', 'NOT FOUND')}")
        logger.info(f"avg_goals_conceded_last_8: {liverpool_stats.get('avg_goals_conceded_last_8', 'NOT FOUND')}")
        logger.info(f"ppg_last_8: {liverpool_stats.get('ppg_last_8', 'NOT FOUND')}")
    else:
        logger.error("Liverpool not found in team_stats!")
    
    # Check Arsenal stats
    arsenal_row = team_stats[team_stats['Team'] == 'Arsenal']
    if not arsenal_row.empty:
        arsenal_stats = arsenal_row.iloc[0]
        logger.info("=== ARSENAL STATS ===")
        logger.info(f"goals_scored_per_match_away: {arsenal_stats.get('goals_scored_per_match_away', 'NOT FOUND')}")
        logger.info(f"goals_conceded_per_match_away: {arsenal_stats.get('goals_conceded_per_match_away', 'NOT FOUND')}")
        logger.info(f"away_recent_scoring_rate: {arsenal_stats.get('away_recent_scoring_rate', 'NOT FOUND')}")
        logger.info(f"away_recent_conceding_rate: {arsenal_stats.get('away_recent_conceding_rate', 'NOT FOUND')}")
        logger.info(f"avg_goals_scored_last_8: {arsenal_stats.get('avg_goals_scored_last_8', 'NOT FOUND')}")
        logger.info(f"avg_goals_conceded_last_8: {arsenal_stats.get('avg_goals_conceded_last_8', 'NOT FOUND')}")
        logger.info(f"ppg_last_8: {arsenal_stats.get('ppg_last_8', 'NOT FOUND')}")
    else:
        logger.error("Arsenal not found in team_stats!")
    
    # Check league stats
    logger.info("=== LEAGUE STATS ===")
    logger.info(f"League stats shape: {league_stats.shape}")
    logger.info(f"League stats columns: {list(league_stats.columns)}")
    
    # Look for home/away goals per match (using correct column name)
    home_goals = league_stats[league_stats['Stat'] == 'home_goals_per_match']
    away_goals = league_stats[league_stats['Stat'] == 'away_goals_per_match']
    avg_goals = league_stats[league_stats['Stat'] == 'avg_goals_per_match']
    
    if not home_goals.empty:
        logger.info(f"home_goals_per_match: {home_goals.iloc[0]['value']}")
    else:
        logger.error("home_goals_per_match not found!")
        
    if not away_goals.empty:
        logger.info(f"away_goals_per_match: {away_goals.iloc[0]['value']}")
    else:
        logger.error("away_goals_per_match not found!")
        
    if not avg_goals.empty:
        logger.info(f"avg_goals_per_match: {avg_goals.iloc[0]['value']}")
    else:
        logger.error("avg_goals_per_match not found!")
    
    # Now test the prediction with this real data
    from prediction import predict_match
    from feature_engineering import prepare_features
    from model_training import train_model
    import pandas as pd
    
    logger.info("=== TESTING PREDICTION WITH REAL DATA ===")
    
    # Prepare features (same as 10-match test)
    prepared_data = prepare_features(
        results, team_stats, league_stats,
        h2h_stats, league_table,
        league_config.get('COLUMN_MAPPING', {}),
        league_config.get('TEAM_NAME_MAPPING', {})
    )
    
    # Train models (simplified)
    X = prepared_data.drop(['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts'], axis=1, errors='ignore')
    y = prepared_data[['three_way', 'over_under_2_5', 'btts']].copy()
    
    # Clean and prepare training data
    for col in X.columns:
        if X[col].dtype == 'object':
            try:
                X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)
            except:
                X[col] = 0
    
    # Remove target leakage features
    leakage_features = [col for col in X.columns if any(target in col for target in ['three_way_encoded', 'over_under_', 'btts_encoded'])]
    X = X.drop(leakage_features, axis=1, errors='ignore')
    
    models = train_model(X, y)
    
    # Test prediction
    try:
        result = predict_match(
            models=models,
            home_team="Liverpool",
            away_team="Arsenal",
            team_stats=team_stats,
            league_stats=league_stats,
            h2h_stats=h2h_stats,
            league_table=league_table,
            column_mapping={},
            feature_names=models.get('three_way', {}).get('feature_names', []) if models.get('three_way') is not None else [],
            avg_goals_per_match=2.5,
            label_encoders={pred_type: models[pred_type].get('encoder') for pred_type in models.keys() if models.get(pred_type) is not None},
            log_features=False
        )
        
        if result and len(result) >= 1:
            predictions = result[0]
            if predictions and 'expected_goals' in predictions:
                xg = predictions['expected_goals']
                logger.info(f"REAL DATA RESULT: Liverpool vs Arsenal = {xg.get('home', 'N/A'):.2f} - {xg.get('away', 'N/A'):.2f}")
            else:
                logger.error("No expected_goals in predictions")
        else:
            logger.error("Prediction failed")
            
    except Exception as e:
        logger.error(f"Prediction error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == '__main__':
    test_debug_data()
