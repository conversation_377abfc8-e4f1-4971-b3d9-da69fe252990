#!/usr/bin/env python3
"""
Debug test to check what's happening with imports in the 10-match test environment
"""

import sys
import os
import logging
from datetime import datetime

# Clear any cached modules (same as 10-match test)
modules_to_clear = [
    'prediction.expected_goals',
    'prediction.core',
    'prediction',
    'feature_engineering.core',
    'feature_engineering',
    'model_training.core',
    'model_training'
]

for module in modules_to_clear:
    if module in sys.modules:
        del sys.modules[module]

# Add src to path (same as 10-match test)
sys.path.append('src')

# Setup logging (same as 10-match test)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_debug_import():
    """Test expected goals import exactly as 10-match test does"""
    
    logger.info("=== DEBUG IMPORT TEST ===")
    
    # Import exactly as 10-match test does
    from prediction.expected_goals import calculate_expected_goals
    
    # Check the file path of the imported module
    import prediction.expected_goals as xg_module
    logger.info(f"Expected goals module file: {xg_module.__file__}")
    
    # Check if our changes are in the source code
    import inspect
    source_lines = inspect.getsource(xg_module.calculate_expected_goals)
    
    # Check for our debug logging
    if "DEBUG - Defense calculation" in source_lines:
        logger.info("✅ DEBUG logging found in source code")
    else:
        logger.info("❌ DEBUG logging NOT found in source code")
    
    # Check for our fixed constants
    if "1.5 / (home_conceding" in source_lines:
        logger.info("✅ Fixed defense calculation found in source code")
    else:
        logger.info("❌ Fixed defense calculation NOT found in source code")
    
    if "1.4 / away_defense" in source_lines:
        logger.info("✅ Fixed xG calculation found in source code")
    else:
        logger.info("❌ Fixed xG calculation NOT found in source code")
    
    # Test with Liverpool vs Arsenal data (same as our working test)
    liverpool_stats = {
        'goals_scored_per_match_home': 2.21,
        'goals_conceded_per_match_home': 0.84,
        'home_recent_scoring_rate': 2.21,
        'home_recent_conceding_rate': 0.84,
        'ppg_last_8': 2.21,
        'home_points_per_game': 2.42,
        'h2h_total_matches': 0,
        'points_difference': 2
    }
    
    arsenal_stats = {
        'goals_scored_per_match_away': 1.79,
        'goals_conceded_per_match_away': 0.89,
        'away_recent_scoring_rate': 1.79,
        'away_recent_conceding_rate': 0.89,
        'ppg_last_8': 1.95,
        'away_points_per_game': 1.84,
        'h2h_total_matches': 0,
        'points_difference': -2
    }
    
    league_stats = {
        'home_goals_per_match': 1.5,
        'away_goals_per_match': 1.0,
        'avg_goals_per_match': 2.5
    }
    
    logger.info("Testing Liverpool vs Arsenal with 10-match test import path...")
    home_xg, away_xg = calculate_expected_goals(liverpool_stats, arsenal_stats, league_stats)
    logger.info(f"RESULT: Liverpool vs Arsenal = {home_xg:.2f} - {away_xg:.2f}")
    
    if abs(home_xg - 2.37) < 0.1 and abs(away_xg - 1.13) < 0.1:
        logger.info("✅ SUCCESS: Expected goals are correct with 10-match test imports!")
    elif abs(home_xg - 0.50) < 0.1 and abs(away_xg - 0.30) < 0.1:
        logger.info("❌ FAILED: Getting the broken values from 10-match test!")
        logger.info("This means there's a different code path or import issue")
    else:
        logger.info("❓ UNKNOWN: Got different values")
        logger.info(f"Expected: ~2.37 - ~1.13, Got: {home_xg:.2f} - {away_xg:.2f}")

if __name__ == '__main__':
    test_debug_import()
