#!/usr/bin/env python3
"""
Debug test to see what's happening with expected goals in the 10-match test
"""

import sys
import os
import logging
from datetime import datetime
import pandas as pd

# Clear any cached modules to ensure fresh imports
modules_to_clear = [
    'prediction.expected_goals',
    'prediction.core',
    'prediction',
    'feature_engineering.core',
    'feature_engineering',
    'model_training.core',
    'model_training'
]

for module in modules_to_clear:
    if module in sys.modules:
        del sys.modules[module]

# Add src to path
sys.path.append('src')

# Import from the working test script approach
from data_loading.core import load_data
from scrapers.config import LEAGUE_CONFIGS
from feature_engineering import prepare_features
from model_training import train_model
from prediction import predict_match

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_debug_xg():
    """Test expected goals calculation using the same path as 10-match test"""
    
    logger.info("=== DEBUG XG TEST ===")
    
    # Load data (same as 10-match test)
    league_config = LEAGUE_CONFIGS['ENGLAND_PREMIER_LEAGUE']
    data = load_data('ENGLAND_PREMIER_LEAGUE', league_config)

    # Check data structure
    logger.info(f"Data structure: {type(data)}, length: {len(data)}")

    if len(data) == 2:
        # New format: (tuple_of_dataframes, league_stats_dict)
        dataframes_tuple, league_stats_dict = data
        results, team_stats, league_stats, h2h_stats, league_table = dataframes_tuple
    else:
        # Old format: tuple of dataframes
        results, team_stats, league_stats, h2h_stats, league_table = data
        league_stats_dict = {}
    
    # Prepare features (same as 10-match test)
    features_df = prepare_features(results, team_stats, league_stats, h2h_stats, league_table)
    
    # Train models (same as 10-match test)
    X = features_df.drop(['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts'], axis=1, errors='ignore')
    y = features_df[['three_way', 'over_under_2_5', 'btts']].copy()
    
    # Clean and prepare training data
    for col in X.columns:
        if X[col].dtype == 'object':
            try:
                X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)
            except:
                X[col] = 0
    
    # Remove target leakage features
    leakage_features = [col for col in X.columns if any(target in col for target in ['three_way_encoded', 'over_under_', 'btts_encoded'])]
    X = X.drop(leakage_features, axis=1, errors='ignore')
    
    models = train_model(X, y)
    
    # Test Liverpool vs Arsenal prediction (same as 10-match test)
    home_team = "Liverpool"
    away_team = "Arsenal"
    
    logger.info(f"Testing prediction for {home_team} vs {away_team}")
    
    # Make prediction using the same function as 10-match test
    predictions = predict_match(
        home_team=home_team,
        away_team=away_team,
        team_stats=team_stats,
        league_stats=league_stats_dict if 'league_stats_dict' in locals() else {},
        h2h_stats=h2h_stats,
        models=models,
        features_df=features_df
    )
    
    # Extract expected goals
    expected_goals = predictions.get('expected_goals', {})
    home_xg = expected_goals.get('home', 0)
    away_xg = expected_goals.get('away', 0)
    
    logger.info(f"RESULT: {home_team} vs {away_team} = {home_xg:.2f} - {away_xg:.2f}")
    
    if abs(home_xg - 2.28) < 0.1 and abs(away_xg - 1.15) < 0.1:
        logger.info("✅ SUCCESS: Expected goals are correct!")
    else:
        logger.info("❌ FAILED: Expected goals are still wrong!")
        logger.info(f"Expected: ~2.28 - ~1.15, Got: {home_xg:.2f} - {away_xg:.2f}")

if __name__ == '__main__':
    test_debug_xg()
