#!/usr/bin/env python3
"""
Test to verify Excel output is working correctly
"""

import pandas as pd
import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_excel_output():
    """Test that Excel file contains correct data"""
    
    excel_path = "data/processed/ENGLAND_PREMIER_LEAGUE/ENGLAND_PREMIER_LEAGUE_match_predictions.xlsx"
    
    try:
        # Read the main sheet
        df = pd.read_excel(excel_path, sheet_name='Main Predictions')
        
        logger.info("=== EXCEL FILE VERIFICATION ===")
        logger.info(f"Excel file loaded successfully: {excel_path}")
        logger.info(f"Number of matches: {len(df)}")
        logger.info(f"Columns: {list(df.columns)}")
        
        # Check first match data
        first_match = df.iloc[0]
        logger.info(f"\n=== FIRST MATCH DATA ===")
        logger.info(f"Home Team: {first_match['Home Team']}")
        logger.info(f"Away Team: {first_match['Away Team']}")
        
        # Check Over/Under predictions
        logger.info(f"\n=== OVER/UNDER PREDICTIONS ===")
        logger.info(f"O/U 1.5 Prediction: {first_match['Over/Under 1.5 Prediction']}")
        logger.info(f"Over 1.5: {first_match['Over 1.5']:.1%}")
        logger.info(f"Under 1.5: {first_match['Under 1.5']:.1%}")
        
        logger.info(f"O/U 2.5 Prediction: {first_match['Over/Under 2.5 Prediction']}")
        logger.info(f"Over 2.5: {first_match['Over 2.5']:.1%}")
        logger.info(f"Under 2.5: {first_match['Under 2.5']:.1%}")
        
        logger.info(f"O/U 3.5 Prediction: {first_match['Over/Under 3.5 Prediction']}")
        logger.info(f"Over 3.5: {first_match['Over 3.5']:.1%}")
        logger.info(f"Under 3.5: {first_match['Under 3.5']:.1%}")
        
        # Check Expected Goals
        logger.info(f"\n=== EXPECTED GOALS ===")
        logger.info(f"Expected Goals Home: {first_match['Expected Goals Home']:.2f}")
        logger.info(f"Expected Goals Away: {first_match['Expected Goals Away']:.2f}")
        
        # Check if values are non-zero
        ou_working = (
            first_match['Over 1.5'] > 0 and 
            first_match['Over 2.5'] > 0 and 
            first_match['Over 3.5'] > 0
        )
        
        xg_working = (
            first_match['Expected Goals Home'] > 0 and 
            first_match['Expected Goals Away'] > 0
        )
        
        logger.info(f"\n=== VERIFICATION RESULTS ===")
        logger.info(f"✅ Over/Under predictions working: {ou_working}")
        logger.info(f"✅ Expected Goals working: {xg_working}")
        
        if ou_working and xg_working:
            logger.info("🎉 ALL EXCEL OUTPUT ISSUES FIXED!")
        else:
            logger.error("❌ Some issues still remain")
            
    except Exception as e:
        logger.error(f"❌ Error reading Excel file: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == '__main__':
    test_excel_output()
