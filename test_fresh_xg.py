#!/usr/bin/env python3
"""
Fresh test of expected goals calculation to verify changes are applied
"""

import sys
import os

# Clear any cached modules
if 'prediction.expected_goals' in sys.modules:
    del sys.modules['prediction.expected_goals']
if 'prediction' in sys.modules:
    del sys.modules['prediction']

# Add src to path
sys.path.insert(0, 'src')

# Fresh import
from prediction.expected_goals import calculate_expected_goals

def test_expected_goals():
    # Liverpool stats (home)
    liverpool_stats = {
        'goals_scored_per_match_home': 2.21,
        'goals_conceded_per_match_home': 0.84,
        'home_recent_scoring_rate': 2.21,
        'home_recent_conceding_rate': 0.84,
        'ppg_last_8': 2.21,
        'home_points_per_game': 2.42,
        'h2h_total_matches': 0,
        'points_difference': 0
    }

    # Arsenal stats (away)
    arsenal_stats = {
        'goals_scored_per_match_away': 1.79,
        'goals_conceded_per_match_away': 0.89,
        'away_recent_scoring_rate': 1.79,
        'away_recent_conceding_rate': 0.89,
        'ppg_last_8': 1.95,
        'away_points_per_game': 1.84,
        'h2h_total_matches': 0,
        'points_difference': 0
    }

    # League stats
    league_stats = {
        'home_goals_per_match': 1.5,
        'away_goals_per_match': 1.0,
        'avg_goals_per_match': 2.5
    }

    print('=== FRESH EXPECTED GOALS TEST ===')
    home_xg, away_xg = calculate_expected_goals(liverpool_stats, arsenal_stats, league_stats)
    print(f'Liverpool vs Arsenal: {home_xg:.2f} - {away_xg:.2f}')
    
    if abs(home_xg - 2.28) < 0.1 and abs(away_xg - 1.15) < 0.1:
        print('✅ SUCCESS: Changes are applied correctly!')
        print(f'Expected: ~2.28 - ~1.15, Got: {home_xg:.2f} - {away_xg:.2f}')
    else:
        print('❌ FAILED: Changes are NOT applied')
        print(f'Expected: ~2.28 - ~1.15, Got: {home_xg:.2f} - {away_xg:.2f}')
        print('This suggests a caching or import issue')

if __name__ == '__main__':
    test_expected_goals()
