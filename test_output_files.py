#!/usr/bin/env python3
"""
Quick test to verify that output files are working correctly
"""

import sys
import os
import logging

# Add src to path
sys.path.append('src')

from prediction.markdown_output import save_predictions_to_markdown
from prediction.excel_output import save_predictions_to_excel

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_output_files():
    """Test output file generation with sample data"""
    
    logger.info("=== TESTING OUTPUT FILES ===")
    
    # Sample prediction data that matches the expected structure
    sample_predictions = {
        "Liverpool vs Arsenal": {
            "main_predictions": {
                "three_way": {
                    "prediction": "Home",
                    "probabilities": {
                        "Home": 0.481,
                        "Draw": 0.339,
                        "Away": 0.180
                    }
                },
                "over_under_2_5": {
                    "prediction": "Over 2.5",
                    "probabilities": {
                        "Over 2.5": 0.781,
                        "Under 2.5": 0.219
                    }
                },
                "btts": {
                    "prediction": "Yes",
                    "probabilities": {
                        "Yes": 0.620,
                        "No": 0.380
                    }
                },
                "double_chance": {
                    "prediction": "Home or Draw",
                    "probabilities": {
                        "Home or Draw": 0.389,
                        "Away or Draw": 0.269,
                        "Home or Away": 0.342
                    }
                }
            },
            "confidence_levels": {
                "three_way": {
                    "confidence_level": "Very Low"
                }
            },
            "risk_assessment": {
                "overall": {
                    "risk_level": "Medium"
                }
            },
            "score_predictions": {
                "home_expected_goals": 2.31,
                "away_expected_goals": 1.36,
                "most_likely_scores": {
                    "2-1": 0.10,
                    "3-1": 0.083,
                    "2-0": 0.075,
                    "1-1": 0.070,
                    "3-0": 0.066
                }
            },
            "form_data_valid_str": "Valid"
        }
    }
    
    # Test markdown output
    logger.info("Testing markdown output...")
    try:
        markdown_path = save_predictions_to_markdown(
            sample_predictions, 
            "TEST_LEAGUE"
        )
        logger.info(f"✅ Markdown file generated: {markdown_path}")
    except Exception as e:
        logger.error(f"❌ Markdown generation failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
    
    # Test Excel output
    logger.info("Testing Excel output...")
    try:
        save_predictions_to_excel(
            sample_predictions, 
            "TEST_LEAGUE"
        )
        logger.info("✅ Excel file generated successfully")
    except Exception as e:
        logger.error(f"❌ Excel generation failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == '__main__':
    test_output_files()
