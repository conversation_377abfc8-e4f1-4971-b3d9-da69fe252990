#!/usr/bin/env python3
"""
Simple test to check expected goals calculation with exact same data as 10-match test
"""

import sys
import logging

# Clear any cached modules
modules_to_clear = [
    'prediction.expected_goals',
    'prediction.core',
    'prediction',
]

for module in modules_to_clear:
    if module in sys.modules:
        del sys.modules[module]

# Add src to path
sys.path.append('src')

# Setup logging
logging.basicConfig(level=logging.INFO)

from prediction.expected_goals import calculate_expected_goals

def test_simple_xg():
    """Test expected goals with the exact same data format as 10-match test"""
    
    print("=== SIMPLE XG TEST ===")
    
    # These are the exact stats that would be passed from the 10-match test
    # Based on the log output showing "Team strengths - Home: 0.81, Away: 0.61"
    
    # Liverpool home stats (from team_stats dataframe)
    liverpool_home_stats = {
        'goals_scored_per_match_home': 2.21,
        'goals_conceded_per_match_home': 0.84,
        'home_recent_scoring_rate': 2.21,
        'home_recent_conceding_rate': 0.84,
        'ppg_last_8': 2.21,
        'home_points_per_game': 2.42,
        'h2h_total_matches': 0,
        'points_difference': 2  # Liverpool has slightly more points than Arsenal (realistic)
    }
    
    # Arsenal away stats (from team_stats dataframe)
    arsenal_away_stats = {
        'goals_scored_per_match_away': 1.79,
        'goals_conceded_per_match_away': 0.89,
        'away_recent_scoring_rate': 1.79,
        'away_recent_conceding_rate': 0.89,
        'ppg_last_8': 1.95,
        'away_points_per_game': 1.84,
        'h2h_total_matches': 0,
        'points_difference': -2  # Arsenal has slightly fewer points than Liverpool (realistic)
    }
    
    # League stats (from league_stats dataframe)
    league_stats_dict = {
        'home_goals_per_match': 1.5,
        'away_goals_per_match': 1.0,
        'avg_goals_per_match': 2.5
    }
    
    print("Testing Liverpool vs Arsenal with exact same data...")
    print(f"Liverpool home scoring: {liverpool_home_stats['goals_scored_per_match_home']}")
    print(f"Arsenal away conceding: {arsenal_away_stats['goals_conceded_per_match_away']}")
    print(f"Arsenal away scoring: {arsenal_away_stats['goals_scored_per_match_away']}")
    print(f"Liverpool home conceding: {liverpool_home_stats['goals_conceded_per_match_home']}")
    
    # Call the function exactly as the 10-match test does
    home_xg, away_xg = calculate_expected_goals(
        liverpool_home_stats, 
        arsenal_away_stats, 
        league_stats_dict
    )
    
    print(f"RESULT: Liverpool vs Arsenal = {home_xg:.2f} - {away_xg:.2f}")
    
    # Check if we get the expected values
    if abs(home_xg - 2.28) < 0.1 and abs(away_xg - 1.15) < 0.1:
        print("✅ SUCCESS: Expected goals are correct!")
        print(f"Expected: ~2.28 - ~1.15, Got: {home_xg:.2f} - {away_xg:.2f}")
    elif abs(home_xg - 1.00) < 0.1 and abs(away_xg - 0.80) < 0.1:
        print("❌ FAILED: Still getting old values!")
        print(f"Got old values: {home_xg:.2f} - {away_xg:.2f}")
        print("This means the changes are not being applied in the 10-match test")
    else:
        print("❓ UNKNOWN: Got different values")
        print(f"Expected: ~2.28 - ~1.15, Got: {home_xg:.2f} - {away_xg:.2f}")

if __name__ == '__main__':
    test_simple_xg()
